import { db, auth } from '../firebase.config';
import { doc, updateDoc, getDoc, collection, query, where, getDocs, orderBy, serverTimestamp } from 'firebase/firestore';
import { createSellerTransactionsFromOrder } from './transactionUtils';

/**
 * Update order status
 * @param {string} orderId - Order ID
 * @param {string} status - New status
 * @param {Object} additionalData - Additional data to update (optional)
 * @returns {Promise<boolean>} - Success status
 */
export const updateOrderStatus = async (orderId, status, additionalData = {}) => {
  try {
    // Validate inputs
    if (!orderId) {
      console.error('updateOrderStatus: Order ID is required');
      throw new Error('Order ID is required');
    }

    if (!status) {
      console.error('updateOrderStatus: Status is required');
      throw new Error('Status is required');
    }

    console.log(`Updating order ${orderId} to status: ${status} with additional data:`, additionalData);

    // Check if the order exists first
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);

    if (!orderDoc.exists()) {
      console.error(`updateOrderStatus: Order ${orderId} does not exist`);
      throw new Error(`Order ${orderId} does not exist`);
    }

    // Prepare update data
    const updateData = {
      orderStatus: status,
      updatedAt: serverTimestamp(),
      ...additionalData
    };

    console.log(`Updating order with data:`, updateData);

    // Update the order
    await updateDoc(orderRef, updateData);
    console.log(`Successfully updated order ${orderId} to status: ${status}`);
    return true;
  } catch (error) {
    console.error(`Error updating order status for order ${orderId}:`, error);
    // Re-throw the error so it can be caught and handled by the caller
    throw error;
  }
};

/**
 * Update order status to shipped with tracking information
 * @param {string} orderId - Order ID
 * @param {string} deliveryService - Delivery service name
 * @param {string} trackingId - Tracking ID
 * @returns {Promise<boolean>} - Success status
 */
export const markOrderAsShipped = async (orderId, deliveryService, trackingId) => {
  try {
    // Validate inputs
    if (!orderId) {
      console.error('markOrderAsShipped: Order ID is required');
      throw new Error('Order ID is required');
    }

    if (!deliveryService || !trackingId) {
      console.error('markOrderAsShipped: Delivery service and tracking ID are required');
      throw new Error('Delivery service and tracking ID are required');
    }

    console.log(`Marking order ${orderId} as shipped with delivery service: ${deliveryService} and tracking ID: ${trackingId}`);

    // Update order with shipping information
    const result = await updateOrderStatus(orderId, 'shipped', {
      deliveryService,
      trackingId,
      trackingNumber: trackingId, // Add both field names for compatibility
      shippedAt: serverTimestamp()
    });

    return result;
  } catch (error) {
    console.error(`Error marking order ${orderId} as shipped:`, error);
    // Re-throw the error so it can be caught and handled by the caller
    throw error;
  }
};

/**
 * Update order status to delivered
 * @param {string} orderId - Order ID
 * @returns {Promise<boolean>} - Success status
 */
export const markOrderAsDelivered = async (orderId) => {
  try {
    if (!orderId) {
      console.error('markOrderAsDelivered: Order ID is required');
      throw new Error('Order ID is required');
    }

    console.log(`Marking order ${orderId} as delivered`);

    const result = await updateOrderStatus(orderId, 'delivered', {
      deliveredAt: serverTimestamp()
    });

    return result;
  } catch (error) {
    console.error(`Error marking order ${orderId} as delivered:`, error);
    // Re-throw the error so it can be caught and handled by the caller
    throw error;
  }
};

/**
 * Update order status to cancelled with reason
 * @param {string} orderId - Order ID
 * @param {string} cancellationReason - Reason for cancellation
 * @param {string} cancelledBy - Who cancelled the order ('buyer' or 'seller')
 * @returns {Promise<boolean>} - Success status
 */
export const cancelOrder = async (orderId, cancellationReason, cancelledBy = 'seller') => {
  try {
    if (!orderId) {
      console.error('cancelOrder: Order ID is required');
      throw new Error('Order ID is required');
    }

    console.log(`Cancelling order ${orderId} with reason: ${cancellationReason}, cancelled by: ${cancelledBy}`);

    const result = await updateOrderStatus(orderId, 'cancelled', {
      cancellationReason,
      cancelledAt: serverTimestamp(),
      cancelledBy // Track who cancelled the order
    });

    return result;
  } catch (error) {
    console.error(`Error cancelling order ${orderId}:`, error);
    // Re-throw the error so it can be caught and handled by the caller
    throw error;
  }
};

/**
 * Buyer cancels an order
 * @param {string} orderId - Order ID
 * @param {string} reason - Cancellation reason
 * @returns {Promise<boolean>} - Success status
 */
export const buyerCancelOrder = async (orderId, reason) => {
  try {
    // Validate inputs
    if (!orderId) {
      console.error('buyerCancelOrder: Order ID is required');
      throw new Error('Order ID is required');
    }

    console.log(`Buyer attempting to cancel order ${orderId} with reason: ${reason}`);

    // Get the order to check if it can be cancelled
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);

    if (!orderDoc.exists()) {
      console.error(`buyerCancelOrder: Order ${orderId} not found`);
      throw new Error('Order not found');
    }

    const orderData = orderDoc.data();
    console.log(`Order status: ${orderData.orderStatus}`);

    // Only allow cancellation if the order is in 'processing' status
    // Once shipped or delivered, it cannot be cancelled by the buyer
    if (orderData.orderStatus !== 'processing') {
      console.error(`buyerCancelOrder: Cannot cancel order in ${orderData.orderStatus} status`);
      throw new Error(`Cannot cancel order in ${orderData.orderStatus} status`);
    }

    // Cancel the order
    const result = await cancelOrder(orderId, reason || 'Cancelled by buyer', 'buyer');

    return result;
  } catch (error) {
    console.error(`Error with buyer cancelling order ${orderId}:`, error);
    // Re-throw the error so it can be caught and handled by the caller
    throw error;
  }
};

/**
 * Get order details
 * @param {string} orderId - Order ID
 * @returns {Promise<Object|null>} - Order data or null if not found
 */
export const getOrderDetails = async (orderId) => {
  try {
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);

    if (!orderDoc.exists()) {
      return null;
    }

    return {
      id: orderDoc.id,
      ...orderDoc.data()
    };
  } catch (error) {
    console.error('Error getting order details:', error);
    return null;
  }
};

/**
 * Get orders for current user (buyer)
 * @returns {Promise<Array>} - Array of orders
 */
export const getBuyerOrders = async () => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const ordersRef = collection(db, 'orders');
    const q = query(
      ordersRef,
      where('userId', '==', currentUser.uid),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error getting buyer orders:', error);
    return [];
  }
};

/**
 * Get orders for current user (seller)
 * @returns {Promise<Array>} - Array of orders
 */
export const getSellerOrders = async () => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const ordersRef = collection(db, 'orders');
    const q = query(
      ordersRef,
      where('sellerId', '==', currentUser.uid),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error getting seller orders:', error);
    return [];
  }
};

/**
 * Mark order as completed and create seller transactions
 * @param {string} orderId - Order ID
 * @returns {Promise<boolean>} - Success status
 */
export const completeOrder = async (orderId) => {
  try {
    if (!orderId) {
      console.error('completeOrder: Order ID is required');
      throw new Error('Order ID is required');
    }

    console.log(`Completing order ${orderId} and creating seller transactions`);

    // First, get the order details
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);

    if (!orderDoc.exists()) {
      console.error(`completeOrder: Order ${orderId} does not exist`);
      throw new Error(`Order ${orderId} does not exist`);
    }

    const orderData = orderDoc.data();

    // Only allow completion if the order is delivered
    if (orderData.orderStatus !== 'delivered') {
      console.error(`completeOrder: Cannot complete order in ${orderData.orderStatus} status`);
      throw new Error(`Cannot complete order in ${orderData.orderStatus} status. Order must be delivered first.`);
    }

    // Update order status to completed
    await updateOrderStatus(orderId, 'completed', {
      completedAt: serverTimestamp()
    });

    // Create seller transactions for this order
    await createSellerTransactionsFromOrder(orderId, orderData);

    console.log(`Successfully completed order ${orderId} and created seller transactions`);
    return true;
  } catch (error) {
    console.error(`Error completing order ${orderId}:`, error);
    throw error;
  }
};
