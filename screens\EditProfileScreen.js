import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Image, ScrollView, Platform, Switch, SafeAreaView, KeyboardAvoidingView, StatusBar } from 'react-native';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import * as ImagePicker from 'expo-image-picker';
import { db, auth } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';

const EditProfileScreen = ({ navigation }) => {
  const [name, setName] = useState('');
  const [bio, setBio] = useState('');
  const [profilePictureUrl, setProfilePictureUrl] = useState(null);
  const [imageUri, setImageUri] = useState(null);
  const [isSeller, setIsSeller] = useState(false);
  const [streetAddress, setStreetAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [country, setCountry] = useState('');
  const [website, setWebsite] = useState('');
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [canChangeUsername, setCanChangeUsername] = useState(true);
  const [timeUntilUsernameChange, setTimeUntilUsernameChange] = useState('');
  const [originalName, setOriginalName] = useState('');
  const user = auth.currentUser;

  useEffect(() => {
    const fetchUserData = async () => {
      if (user) {
        const userDocRef = doc(db, 'users', user.uid);
        try {
          const docSnap = await getDoc(userDocRef);
          if (docSnap.exists()) {
            const data = docSnap.data();
            setName(data.name || '');
            setOriginalName(data.name || '');
            setBio(data.bio || '');
            setProfilePictureUrl(data.profilePictureUrl);
            setImageUri(data.profilePictureUrl);
            setIsSeller(data.isSeller || false);

            // Set address fields if they exist
            if (data.address) {
              setStreetAddress(data.address.street || '');
              setCity(data.address.city || '');
              setState(data.address.state || '');
              setZipCode(data.address.zipCode || '');
              setCountry(data.address.country || '');
            }

            setWebsite(data.website || '');

            // Check if user can change username (6 months since last change)
            if (data.lastUsernameChangeDate) {
              const lastChangeDate = data.lastUsernameChangeDate.toDate();
              const sixMonthsLater = new Date(lastChangeDate);
              sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6);

              const now = new Date();
              const canChange = now >= sixMonthsLater;
              setCanChangeUsername(canChange);

              if (!canChange) {
                // Calculate time remaining
                const timeRemaining = sixMonthsLater.getTime() - now.getTime();
                const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));
                setTimeUntilUsernameChange(`${daysRemaining} days`);
              }
            } else {
              // If no record of last username change, allow changing
              setCanChangeUsername(true);
            }
          } else {
            Alert.alert("Error", "Could not find your profile data.");
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
          Alert.alert("Error", "Could not load profile data.");
        } finally {
          setLoading(false);
        }
      }
    };
    fetchUserData();
  }, [user]);

  const pickImage = async () => {
    console.log("pickImage function called");
    if (Platform.OS !== 'web') {
      console.log("Requesting media library permissions..."); // Log before requesting permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log("Media library permission status:", status); // Log the permission status
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to make this work!');
        return;
      }
    }

    console.log("Launching image library..."); // Log before launching image library
    let result;
    try {
      result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });
    } catch (pickerError) {
      console.error("Error launching ImagePicker:", pickerError);
      Alert.alert("Picker Error", "Failed to open image library. Please try again or check app permissions.");
      return; // Exit if picker fails to launch
    }

    console.log("ImagePicker result:", JSON.stringify(result, null, 2)); // Add this line to log the full result

    // On some versions result.assets contains selected images
    if (result.assets && result.assets.length > 0) {
      setImageUri(result.assets[0].uri);
    }
  };

  const uploadImage = async (uri) => {
    if (!uri || uri === profilePictureUrl) {
        return profilePictureUrl;
    }

    setUploading(true);
    const response = await fetch(uri);
    const blob = await response.blob();
    const storage = getStorage();
    const storageRef = ref(storage, `profilePictures/${user.uid}/${Date.now()}`);

    try {
      const uploadTask = uploadBytesResumable(storageRef, blob);

      return new Promise((resolve, reject) => {
        uploadTask.on('state_changed',
          (snapshot) => {},
          (error) => {
            console.error("Upload failed:", error);
            Alert.alert("Upload Failed", "Could not upload profile picture.");
            setUploading(false);
            reject(error);
          },
          () => {
            getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
              console.log('File available at', downloadURL);
              setUploading(false);
              resolve(downloadURL);
            });
          }
        );
      });
    } catch (error) {
        console.error("Error during image upload setup:", error);
        Alert.alert("Upload Error", "An unexpected error occurred during upload setup.");
        setUploading(false);
        return null;
    }
  };

  const handleSaveChanges = async () => {
    if (!user) return;
    if (!name.trim()) {
        Alert.alert("Validation Error", "Name cannot be empty.");
        return;
    }

    // Check if username is being changed
    const isUsernameChanged = name.trim() !== originalName;

    // If username is being changed and it's not allowed, warn the user but still allow other updates
    let updateUsername = true;
    if (isUsernameChanged && !canChangeUsername) {
      // Ask user if they want to continue with other updates without changing username
      Alert.alert(
        'Username Change Restricted',
        `You can only change your username once every 6 months. You can change it again in ${timeUntilUsernameChange}. Do you want to continue with other profile updates without changing your username?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => { return; }
          },
          {
            text: 'Continue',
            onPress: () => {
              // Continue with updates but don't change username
              updateUsername = false;
              proceedWithUpdate(false);
            }
          }
        ]
      );
      return; // Return here and let the alert handle the flow
    }

    // If username change is allowed or not being changed, proceed normally
    proceedWithUpdate(true);

    async function proceedWithUpdate(includeUsernameUpdate) {
      setLoading(true);

      try {
          let newProfilePictureUrl = profilePictureUrl;

          if (imageUri && imageUri !== profilePictureUrl) {
              newProfilePictureUrl = await uploadImage(imageUri);
              if (newProfilePictureUrl === null) {
                  setLoading(false);
                  return;
              }
          }

        const userDocRef = doc(db, 'users', user.uid);
        // Prepare update data
        const updateData = {
          bio: bio.trim(),
          profilePictureUrl: newProfilePictureUrl,
          // Keep existing seller status - cannot be changed by user
        };

        // Only include name updates if allowed
        if (includeUsernameUpdate) {
          updateData.name = name.trim();
          updateData.name_lowercase = name.trim().toLowerCase(); // Update lowercase name as well

          // If username is changed, update the lastUsernameChangeDate
          if (isUsernameChanged) {
            updateData.lastUsernameChangeDate = new Date();
          }
        }

      // Add address and website only if the user is a seller
      if (isSeller) {
        updateData.address = {
          street: streetAddress.trim(),
          city: city.trim(),
          state: state.trim(),
          zipCode: zipCode.trim(),
          country: country.trim(),
        };
        updateData.website = website.trim();
      } else {
        // If not a seller, clear address and website
        updateData.address = null;
        updateData.website = '';
      }

      await updateDoc(userDocRef, updateData);

      Alert.alert("Success", "Profile updated successfully!");

      // Try to navigate back safely
      try {
        navigation.goBack();
      } catch (error) {
        console.error("Error navigating back:", error);
        // If goBack fails, try to navigate to the main app
        try {
          navigation.navigate('MainApp');
        } catch (navError) {
          console.error("Error navigating to MainApp:", navError);
          // Last resort - reset navigation to MainApp
          navigation.reset({
            index: 0,
            routes: [{ name: 'MainApp' }],
          });
        }
      }

    } catch (error) {
      console.error("Error updating profile:", error);
      Alert.alert("Error", "Could not update profile.");
    } finally {
      setLoading(false);
      setUploading(false);
    }
    } // Close proceedWithUpdate function
  };

  if (loading && !uploading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B6B" />
      </View>
    );
  }
  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: '#f8f8f8',
      paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0
    }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}      >
        <ScrollView
          style={styles.container}
          contentContainerStyle={{ paddingBottom: 50 }}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.header}>
          <TouchableOpacity
            onPress={() => {
              try {
                navigation.goBack();
              } catch (error) {
                console.error("Error navigating back:", error);
                // If goBack fails, try to navigate to the main app
                try {
                  navigation.navigate('MainApp');
                } catch (navError) {
                  console.error("Error navigating to MainApp:", navError);
                  // Last resort - reset navigation to MainApp
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'MainApp' }],
                  });
                }
              }
            }}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity onPress={handleSaveChanges} disabled={loading || uploading} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>

      <View style={styles.imageContainer}>
        <Image
          source={{ uri: imageUri || 'https://via.placeholder.com/150' }}
          style={styles.profileImage}
        />
        <TouchableOpacity style={styles.changeImageButton} onPress={pickImage} disabled={uploading}>
          <Text style={styles.changeImageButtonText}>Change Photo</Text>
        </TouchableOpacity>
        {uploading && <ActivityIndicator size="small" color="#FF6B6B" style={{ marginTop: 10 }} />}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Name</Text>
        {!canChangeUsername && (
          <View style={styles.restrictionBanner}>
            <Ionicons name="time-outline" size={18} color="#666" />
            <Text style={styles.restrictionText}>
              You can change your username once every 6 months.
              {timeUntilUsernameChange ? ` Next change available in ${timeUntilUsernameChange}.` : ''}
            </Text>
          </View>
        )}        <TextInput
          style={[styles.input, !canChangeUsername && styles.disabledInput]}
          value={name}
          onChangeText={setName}
          placeholder="Your Name"
          autoCapitalize="words"
          editable={canChangeUsername && !uploading}
          placeholderTextColor="#000"
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Bio</Text>        <TextInput
          style={[styles.input, styles.bioInput]}
          value={bio}
          onChangeText={setBio}
          placeholder="Tell us about yourself"
          multiline
          numberOfLines={4}
          editable={!uploading}
          placeholderTextColor="#000"
        />
      </View>

      {/* Seller status display - not editable for buyers */}
      <View style={styles.inputContainer}>
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Seller Status</Text>
          <Text style={styles.sellerStatusText}>{isSeller ? "Seller" : "Buyer"}</Text>
        </View>
      </View>

      {/* Conditional Seller Fields */}
      {isSeller && (
        <>
          <Text style={styles.sectionTitle}>Seller Information</Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Street Address</Text>            <TextInput
              style={styles.input}
              value={streetAddress}
              onChangeText={setStreetAddress}
              placeholder="Street Address"
              autoCapitalize="words"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>City</Text>
            <TextInput
              style={styles.input}
              value={city}
              onChangeText={setCity}
              placeholder="City"
              autoCapitalize="words"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>State/Province</Text>
            <TextInput
              style={styles.input}
              value={state}
              onChangeText={setState}
              placeholder="State/Province"
              autoCapitalize="words"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Zip/Postal Code</Text>
            <TextInput
              style={styles.input}
              value={zipCode}
              onChangeText={setZipCode}
              placeholder="Zip/Postal Code"
              keyboardType="numeric"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Country</Text>
            <TextInput
              style={styles.input}
              value={country}
              onChangeText={setCountry}
              placeholder="Country"
              autoCapitalize="words"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Website (Optional)</Text>
            <TextInput
              style={styles.input}
              value={website}
              onChangeText={setWebsite}
              placeholder="https://yourstore.com"
              autoCapitalize="none"
              keyboardType="url"
              editable={!uploading}
              placeholderTextColor="#000"
            />
          </View>
        </>
      )}

      {(loading && !uploading) && <ActivityIndicator size="large" color="#FF6B6B" style={{ marginTop: 20 }} />}

        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 15,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    padding: 5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  imageContainer: {
    alignItems: 'center',
    marginVertical: 30,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 15,
    backgroundColor: '#eee',
  },
  changeImageButton: {},
  changeImageButtonText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: 'bold',
  },
  inputContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
    fontWeight: 'bold',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  bioInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingVertical: 10,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  sellerStatusText: {
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
    marginTop: 10,
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  restrictionBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#eee',
  },
  restrictionText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
  disabledInput: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ddd',
    color: '#999',
  },
});

export default EditProfileScreen;