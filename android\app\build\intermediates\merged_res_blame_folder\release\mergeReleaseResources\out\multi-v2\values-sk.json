{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,333,411,503,631,712,777,876,952,1017,1107,1173,1227,1296,1356,1410,1527,1587,1649,1703,1775,1905,1992,2084,2193,2262,2340,2428,2495,2561,2633,2710,2793,2865,2942,3015,3086,3174,3246,3338,3434,3508,3582,3678,3730,3797,3884,3971,4033,4097,4160,4266,4362,4460,4558", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "328,406,498,626,707,772,871,947,1012,1102,1168,1222,1291,1351,1405,1522,1582,1644,1698,1770,1900,1987,2079,2188,2257,2335,2423,2490,2556,2628,2705,2788,2860,2937,3010,3081,3169,3241,3333,3429,3503,3577,3673,3725,3792,3879,3966,4028,4092,4155,4261,4357,4455,4553,4632"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3170,3985,4077,4205,6743,6808,7298,7532,7665,7755,7821,7875,7944,8004,8058,8175,8235,8297,8351,8423,8784,8871,8963,9072,9141,9219,9307,9374,9440,9512,9589,9672,9744,9821,9894,9965,10053,10125,10217,10313,10387,10461,10557,10609,10676,10763,10850,10912,10976,11039,11145,11241,11339,11437", "endLines": "7,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "378,3243,4072,4200,4281,6803,6902,7369,7592,7750,7816,7870,7939,7999,8053,8170,8230,8292,8346,8418,8548,8866,8958,9067,9136,9214,9302,9369,9435,9507,9584,9667,9739,9816,9889,9960,10048,10120,10212,10308,10382,10456,10552,10604,10671,10758,10845,10907,10971,11034,11140,11236,11334,11432,11511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45da6d36d3d2ef1e836ae57a6da35b9a\\transformed\\react-android-0.79.2-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "35,47,72,74,75,77,90,91,92,127,128,129,130,132,133,134,135,136,137,138,139,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3095,4286,7227,7374,7445,7597,8553,8622,8703,11516,11603,11698,11772,11941,12025,12102,12183,12265,12343,12418,12492,12677,12748,12827,12898", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3165,4364,7293,7440,7527,7660,8617,8698,8779,11598,11693,11767,11853,12020,12097,12178,12260,12338,12413,12487,12571,12743,12822,12893,12976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "145", "startColumns": "4", "startOffsets": "12981", "endColumns": "75", "endOffsets": "13052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "383,490,591,702,788,896,1014,1093,1170,1261,1354,1452,1546,1646,1739,1834,1932,2023,2114,2198,2303,2411,2510,2616,2728,2831,2997,11858", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "485,586,697,783,891,1009,1088,1165,1256,1349,1447,1541,1641,1734,1829,1927,2018,2109,2193,2298,2406,2505,2611,2723,2826,2992,3090,11936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "37,38,39,40,41,42,43,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3248,3344,3446,3547,3645,3755,3863,12576", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3339,3441,3542,3640,3750,3858,3980,12672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5368", "endColumns": "138", "endOffsets": "5502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4369,4475,4635,4759,4869,5025,5153,5266,5507,5676,5787,5957,6087,6250,6414,6482,6549", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "4470,4630,4754,4864,5020,5148,5261,5363,5671,5782,5952,6082,6245,6409,6477,6544,6631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6636,6907,7010,7125", "endColumns": "106,102,114,101", "endOffsets": "6738,7005,7120,7222"}}]}]}