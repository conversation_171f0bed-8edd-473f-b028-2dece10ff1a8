import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  Modal,
  StatusBar
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { collection, addDoc, serverTimestamp, doc, getDoc, getDocs, deleteDoc } from 'firebase/firestore';
import { db, auth, storage } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import HybridCategorySelector from '../components/HybridCategorySelector';
import ColorSelector from '../components/ColorSelector';
import { validateCategorySelection, getBroadCategories } from '../utils/categoryHierarchy';

// Default categories - using the complete category hierarchy
const defaultCategories = getBroadCategories();

const UploadScreen = ({ navigation }) => {
  const [images, setImages] = useState([]); // Array of image URIs
  const [selectedImageIndex, setSelectedImageIndex] = useState(0); // Track which image is displayed in the preview

  // Category state - single broad category, multiple detailed categories
  const [selectedBroadCategory, setSelectedBroadCategory] = useState('');
  const [selectedDetailedCategories, setSelectedDetailedCategories] = useState([]);

  // Gender selection state
  const [selectedGender, setSelectedGender] = useState('');

  // Color selection state (multiple colors)
  const [selectedColors, setSelectedColors] = useState([]);

  const [tags, setTags] = useState(''); // Additional tags for better matching

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [brand, setBrand] = useState('');
  const [size, setSize] = useState('');
  const [buyLink, setBuyLink] = useState('');
  const [price, setPrice] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentUploadIndex, setCurrentUploadIndex] = useState(0); // Track which image is being uploaded
  const [loading, setLoading] = useState(true); // Start with loading true
  const [isSeller, setIsSeller] = useState(false);

  // Legacy category support (for backward compatibility)
  const [categories, setCategories] = useState([...defaultCategories]);
  const [customCategories, setCustomCategories] = useState([]);
  const [customCategoryDocs, setCustomCategoryDocs] = useState([]); // Store category docs with IDs
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [addingCategory, setAddingCategory] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const user = auth.currentUser;

  useEffect(() => {
    // Check if user is a seller
    const checkSellerStatus = async () => {
      if (user) {
        try {
          const userDocRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setIsSeller(userData.isSeller || false);
          } else {
            setIsSeller(false);
          }
        } catch (error) {
          console.error('Error checking seller status:', error);
          setIsSeller(false);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
        setIsSeller(false);
      }
    };

    // Request camera roll permissions
    const requestPermissions = async () => {
      if (Platform.OS !== 'web') {
        try {
          console.log('Requesting initial media library permissions...');
          const libraryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
          console.log('Initial media library permission status:', libraryStatus.status);

          if (libraryStatus.status !== 'granted') {
            console.log('Media library permission not granted');
            Alert.alert(
              'Permission Required',
              'This app needs access to your photo library to upload clothing images. Please grant permission when prompted or enable it in your device settings.'
            );
          } else {
            console.log('Media library permission granted');
          }
        } catch (error) {
          console.error('Error requesting permissions:', error);
        }
      }
    };

    checkSellerStatus();
    requestPermissions();
    fetchCustomCategories();
  }, [user]);

  // Fetch custom categories from Firestore
  const fetchCustomCategories = async () => {
    if (!user) return;

    try {
      // Get user's custom categories
      const userCategoriesRef = collection(db, 'users', user.uid, 'customCategories');
      const querySnapshot = await getDocs(userCategoriesRef);

      // Store both the category names and the full document data with IDs
      const userCustomCategoryDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name,
        createdAt: doc.data().createdAt
      }));

      const userCustomCategories = userCustomCategoryDocs.map(doc => doc.name);

      // Get global custom categories
      const globalCategoriesRef = collection(db, 'customCategories');
      const globalSnapshot = await getDocs(globalCategoriesRef);

      const globalCustomCategories = globalSnapshot.docs.map(doc => doc.data().name);

      // Combine default, user custom, and global custom categories without duplicates
      const allCategories = [...new Set([...defaultCategories, ...userCustomCategories, ...globalCustomCategories])];

      setCategories(allCategories);
      setCustomCategories([...userCustomCategories]);
      setCustomCategoryDocs(userCustomCategoryDocs);
    } catch (error) {
      console.error('Error fetching custom categories:', error);
    }
  };

  // Add a new custom category
  const addCustomCategory = async () => {
    if (!newCategoryName.trim()) {
      Alert.alert('Error', 'Please enter a category name');
      return;
    }

    const categoryName = newCategoryName.trim();

    // Check if category already exists
    if (categories.includes(categoryName)) {
      Alert.alert('Error', 'This category already exists');
      return;
    }

    setAddingCategory(true);

    try {
      // Add to user's custom categories
      const userCategoriesRef = collection(db, 'users', user.uid, 'customCategories');
      const docRef = await addDoc(userCategoriesRef, {
        name: categoryName,
        createdAt: serverTimestamp()
      });

      // Update local state with the new category document
      const newCategoryDoc = {
        id: docRef.id,
        name: categoryName,
        createdAt: new Date() // Use current date as placeholder until refresh
      };

      setCustomCategoryDocs(prev => [...prev, newCategoryDoc]);
      setCategories(prev => [...prev, categoryName]);
      setCustomCategories(prev => [...prev, categoryName]);
      setNewCategoryName('');
      setShowAddCategoryModal(false);

      // Set the newly added category as selected
      setCategory(categoryName);

      Alert.alert('Success', `Category "${categoryName}" added successfully`);
    } catch (error) {
      console.error('Error adding custom category:', error);
      Alert.alert('Error', 'Failed to add custom category');
    } finally {
      setAddingCategory(false);
    }
  };

  // Delete a custom category
  const deleteCategory = async (categoryDoc) => {
    Alert.alert(
      'Delete Category',
      `Are you sure you want to delete the category "${categoryDoc.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete the category from Firestore
              const categoryDocRef = doc(db, 'users', user.uid, 'customCategories', categoryDoc.id);
              await deleteDoc(categoryDocRef);

              // Update local state
              setCustomCategoryDocs(prev => prev.filter(cat => cat.id !== categoryDoc.id));
              setCategories(prev => prev.filter(cat => cat !== categoryDoc.name));
              setCustomCategories(prev => prev.filter(cat => cat !== categoryDoc.name));

              // If the deleted category was selected, clear the selection
              if (category === categoryDoc.name) {
                setCategory('');
              }

              Alert.alert('Success', `Category "${categoryDoc.name}" deleted successfully`);
            } catch (error) {
              console.error('Error deleting custom category:', error);
              Alert.alert('Error', 'Failed to delete custom category');
            }
          }
        }
      ]
    );
  };

  const showImagePickerOptions = () => {
    setShowImagePickerModal(true);
  };

  const pickImageFromLibrary = async () => {
    setShowImagePickerModal(false);
    try {
      console.log('pickImageFromLibrary function called');

      // Check permissions first
      if (Platform.OS !== 'web') {
        console.log('Requesting media library permissions...');
        const libraryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
        console.log('Media library permission status:', libraryStatus.status);

        if (libraryStatus.status !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Sorry, we need camera roll permissions to upload images! Please enable permissions in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => console.log('User should open settings') }
            ]
          );
          return;
        }
      }

      console.log('Launching image library...');
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsMultipleSelection: true, // Allow multiple image selection
        selectionLimit: 5, // Limit to 5 images
        quality: 0.7,
      });

      console.log('ImagePicker result:', JSON.stringify(result, null, 2));
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error in pickImageFromLibrary:', error);
      Alert.alert(
        'Image Picker Error',
        `Failed to open image library: ${error.message}. Please try again or check app permissions.`
      );
    }
  };

  const pickImageFromCamera = async () => {
    setShowImagePickerModal(false);
    try {
      console.log('pickImageFromCamera function called');

      // Check camera permissions first
      if (Platform.OS !== 'web') {
        console.log('Requesting camera permissions...');
        const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
        console.log('Camera permission status:', cameraStatus.status);

        if (cameraStatus.status !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Sorry, we need camera permissions to take photos! Please enable permissions in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => console.log('User should open settings') }
            ]
          );
          return;
        }
      }

      console.log('Launching camera...');
      let result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        quality: 0.7,
      });

      console.log('Camera result:', JSON.stringify(result, null, 2));
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error in pickImageFromCamera:', error);
      Alert.alert(
        'Camera Error',
        `Failed to open camera: ${error.message}. Please try again or check app permissions.`
      );
    }
  };

  const handleImagePickerResult = (result) => {
    if (!result.canceled && result.assets && result.assets.length > 0) {
      console.log('Images selected:', result.assets.length);
      // Add new images to the existing array
      const newImages = result.assets.map(asset => asset.uri);

      setImages(prevImages => {
        // Combine previous and new images, limit to 5 total
        const combinedImages = [...prevImages, ...newImages];
        const finalImages = combinedImages.slice(0, 5); // Limit to 5 images

        console.log('Final images count:', finalImages.length);

        // If this is the first image being added, set it as selected
        if (prevImages.length === 0 && finalImages.length > 0) {
          setSelectedImageIndex(0);
        }

        return finalImages;
      });
    } else {
      console.log('No images selected or operation was canceled');
      if (result.canceled) {
        console.log('User canceled image selection');
      } else {
        console.log('No assets found in result');
      }
    }
  };

  // Keep the old function name for backward compatibility
  const pickImage = showImagePickerOptions;

  const removeImage = (index) => {
    setImages(prevImages => prevImages.filter((_, i) => i !== index));
  };

  const uploadImageAndGetURL = async (uri) => {
    if (!uri) return null;
    setUploading(true);
    setUploadProgress(0);

    // Compress and resize image before upload
    let compressedUri = uri;
    try {
      const manipResult = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1080 } }], // Resize to max width 1080px
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );
      compressedUri = manipResult.uri;
    } catch (err) {
      console.warn('Image compression failed, uploading original:', err);
    }

    const response = await fetch(compressedUri);
    const blob = await response.blob();
    const fileExtension = compressedUri.split('.').pop();
    const fileName = `${Date.now()}_${Math.floor(Math.random() * 1000)}.${fileExtension}`;
    const storageRef = ref(storage, `clothingItems/${user.uid}/${fileName}`);

    return new Promise((resolve, reject) => {
      const uploadTask = uploadBytesResumable(storageRef, blob);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setUploadProgress(progress);
        },
        (error) => {
          console.error('Upload failed:', error);
          Alert.alert('Upload Failed', 'Could not upload image. Please try again.');
          setUploading(false);
          setUploadProgress(0);
          reject(error);
        },
        () => {
          getDownloadURL(uploadTask.snapshot.ref)
            .then((downloadURL) => {
              setUploading(false);
              resolve(downloadURL);
            })
            .catch((error) => {
              console.error('Failed to get download URL:', error);
              Alert.alert('Upload Failed', 'Could not get image URL after upload.');
              setUploading(false);
              reject(error);
            });
        }
      );
    });
  };

  // Upload multiple images and return array of URLs
  const uploadMultipleImages = async (imageUris) => {
    if (!imageUris || imageUris.length === 0) return [];

    const imageUrls = [];
    setUploading(true);

    try {
      // Upload each image sequentially
      for (let i = 0; i < imageUris.length; i++) {
        setCurrentUploadIndex(i);
        setUploadProgress(0);

        // Update progress message
        const progressMessage = `Uploading image ${i + 1} of ${imageUris.length}`;
        console.log(progressMessage);

        const imageUrl = await uploadImageAndGetURL(imageUris[i]);
        if (imageUrl) {
          imageUrls.push(imageUrl);
        }
      }

      return imageUrls;
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      Alert.alert('Upload Failed', 'Could not upload all images. Please try again.');
      return imageUrls; // Return any successfully uploaded images
    } finally {
      setUploading(false);
      setCurrentUploadIndex(0);
    }
  };

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert('Not Logged In', 'You must be logged in to upload items.');
      return;
    }
    if (images.length === 0) {
      Alert.alert('Missing Images', 'Please select at least one image for the item.');
      return;
    }

    // Validate new category structure
    if (!selectedBroadCategory) {
      Alert.alert('Missing Category', 'Please select a category type.');
      return;
    }
    if (selectedDetailedCategories.length === 0) {
      Alert.alert('Missing Categories', 'Please select at least one specific category.');
      return;
    }

    // Validate gender selection
    if (!selectedGender) {
      Alert.alert('Missing Gender', 'Please select a gender category (Male, Female, or Unisex).');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Missing Title', 'Please provide a title for your item.');
      return;
    }
    if (!description.trim()) {
      Alert.alert('Missing Description', 'Please provide a description for your item.');
      return;
    }
    if (!brand.trim()) {
      Alert.alert('Missing Brand', 'Please provide the brand name for your item.');
      return;
    } if (!size.trim()) {
      Alert.alert('Missing Size', 'Please provide the size for your item.');
      return;
    }
    if (!price.trim()) {
      Alert.alert('Missing Price', 'Please provide a price for your item.');
      return;
    }
    // Validate that price is a valid number
    const priceValue = parseFloat(price.trim());
    if (isNaN(priceValue) || priceValue <= 0) {
      Alert.alert('Invalid Price', 'Please provide a valid price for your item.');
      return;
    }

    setLoading(true);
    try {
      // Upload all images and get their URLs
      const imageUrls = await uploadMultipleImages(images);

      if (imageUrls.length === 0) {
        Alert.alert('Upload Failed', 'Could not upload any images. Please try again.');
        setLoading(false);
        return;
      }

      // Process tags
      const processedTags = tags.trim()
        ? tags.split(',').map(tag => tag.trim().toLowerCase()).filter(tag => tag.length > 0)
        : [];

      const itemsCollectionRef = collection(db, 'clothingItems');
      const newItem = {
        userId: user.uid, // Ensure userId is set for uploads
        uploaderId: user.uid, // Also set uploaderId for consistency
        imageUrls: imageUrls, // Array of image URLs
        imageUrl: imageUrls[0], // Keep the first image as the main image for backward compatibility

        // New category structure: single broad, multiple detailed
        broadCategories: selectedBroadCategory ? [selectedBroadCategory] : [],
        detailedCategories: selectedDetailedCategories,

        // Gender support
        gender: selectedGender,

        // Color support (multiple colors)
        colors: selectedColors,

        // Hierarchical categories (backward compatibility)
        broadCategory: selectedBroadCategory,
        detailedCategory: selectedDetailedCategories[0] || '',
        tags: processedTags,

        // Legacy category field for backward compatibility
        category: selectedDetailedCategories[0] || '',

        title: title.trim(),
        description: description.trim(),
        brand: brand.trim(),
        size: size.trim(),
        buyLink: buyLink.trim(),
        price: parseFloat(price.trim()), // Store price as a number
        createdAt: serverTimestamp(),
        likeCount: 0,
        comments: [], // Initialize empty comments array
        inventory: 1, // Initialize inventory count
        status: 'active', // Set initial status
      };
      await addDoc(itemsCollectionRef, newItem);

      Alert.alert('Success', 'Item uploaded successfully!');
      setImages([]);
      setSelectedBroadCategory('');
      setSelectedDetailedCategories([]);
      setSelectedGender('');
      setSelectedColors([]);
      setTags('');
      setTitle('');
      setDescription('');
      setBrand('');
      setSize('');
      setBuyLink('');
      setPrice('');
      setUploadProgress(0);
      navigation.goBack();
    } catch (error) {
      console.error('Error submitting item:', error);
      Alert.alert('Submission Error', 'Could not save item details.');
    } finally {
      setLoading(false);
      setUploading(false);
    }
  };

  // If user is not a seller, show a message
  if (!loading && !isSeller) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Upload Item</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.notSellerContainer}>
          <Ionicons name="alert-circle-outline" size={60} color="#FF6B6B" />
          <Text style={styles.notSellerTitle}>Seller Account Required</Text>
          <Text style={styles.notSellerText}>
            Only sellers can upload items. This feature is not available for buyer accounts.
          </Text>
          <TouchableOpacity
            style={styles.becomeSellerButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.becomeSellerButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Show loading indicator while checking seller status
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={{ marginTop: 10, color: '#555' }}>Loading...</Text>
      </SafeAreaView>
    );
  }
  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: '#fff',
      paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0
    }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          style={styles.container}
          contentContainerStyle={{ paddingBottom: 20, paddingTop: 0 }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={styles.backButton}
              disabled={uploading}
            >
              <Ionicons name="arrow-back" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Upload Item</Text>
            <View style={{ width: 40 }} />
          </View>

          <View style={styles.imageSection}>
            <TouchableOpacity
              style={[styles.imagePicker, images.length > 0 && styles.smallImagePicker]}
              onPress={() => {
                console.log('Image picker button pressed');
                pickImage();
              }}
              disabled={uploading}
            >
              {images.length > 0 ? (
                <Image source={{ uri: images[selectedImageIndex] }} style={styles.imagePreview} />
              ) : (
                <View style={styles.imagePlaceholder}>
                  <Ionicons name="camera" size={40} color="#ccc" />
                  <Text style={styles.imagePlaceholderText}>Select Images</Text>
                  <Text style={styles.imagePlaceholderSubText}>(Up to 5)</Text>
                </View>
              )}

              {images.length > 0 && (
                <View style={styles.imageCountBadge}>
                  <Text style={styles.imageCountText}>{images.length}/5</Text>
                </View>
              )}
            </TouchableOpacity>

            {images.length > 0 && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.thumbnailsContainer}
                contentContainerStyle={styles.thumbnailsContent}
              >
                {images.map((uri, index) => (
                  <View key={index} style={styles.thumbnailWrapper}>
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => setSelectedImageIndex(index)}
                      disabled={uploading || loading}
                      style={[
                        styles.thumbnailTouch,
                        selectedImageIndex === index && styles.selectedThumbnail
                      ]}
                    >
                      <Image source={{ uri }} style={styles.thumbnail} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.removeThumbnailButton}
                      onPress={() => {
                        // If removing the currently selected image, reset to first image
                        if (selectedImageIndex === index) {
                          setSelectedImageIndex(0);
                        }
                        // If removing an image before the selected one, adjust the index
                        else if (selectedImageIndex > index) {
                          setSelectedImageIndex(selectedImageIndex - 1);
                        }
                        removeImage(index);
                      }}
                      disabled={uploading || loading}
                    >
                      <Ionicons name="close-circle" size={22} color="#FF6B6B" />
                    </TouchableOpacity>
                  </View>
                ))}

                {images.length < 5 && (
                  <TouchableOpacity
                    style={styles.addMoreImagesButton}
                    onPress={() => {
                      console.log('Add more images button pressed');
                      showImagePickerOptions();
                    }}
                    disabled={uploading || loading}
                  >
                    <Ionicons name="add-circle" size={24} color="#FF6B6B" />
                    <Text style={styles.addMoreImagesText}>Add More</Text>
                  </TouchableOpacity>
                )}
              </ScrollView>
            )}
          </View>

          {uploading && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                Uploading image {currentUploadIndex + 1} of {images.length}: {uploadProgress.toFixed(0)}%
              </Text>
              <ActivityIndicator size="small" color="#FF6B6B" />
            </View>
          )}

          {/* Category Selector */}
          <View style={styles.categorySection}>
            <HybridCategorySelector
              selectedBroadCategory={selectedBroadCategory}
              selectedDetailedCategories={selectedDetailedCategories}
              onBroadCategorySelect={setSelectedBroadCategory}
              onDetailedCategoriesChange={setSelectedDetailedCategories}
              disabled={loading || uploading}
              style={styles.categorySelector}
            />
          </View>

          {/* Gender Selector */}
          <View style={styles.genderSection}>
            <Text style={styles.label}>Gender</Text>
            <View style={styles.genderOptions}>
              <TouchableOpacity
                style={[styles.genderOption, selectedGender === 'male' && styles.selectedGenderOption]}
                onPress={() => setSelectedGender('male')}
                disabled={loading || uploading}
              >
                <Text style={[styles.genderOptionText, selectedGender === 'male' && styles.selectedGenderOptionText]}>
                  Male
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.genderOption, selectedGender === 'female' && styles.selectedGenderOption]}
                onPress={() => setSelectedGender('female')}
                disabled={loading || uploading}
              >
                <Text style={[styles.genderOptionText, selectedGender === 'female' && styles.selectedGenderOptionText]}>
                  Female
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.genderOption, selectedGender === 'unisex' && styles.selectedGenderOption]}
                onPress={() => setSelectedGender('unisex')}
                disabled={loading || uploading}
              >
                <Text style={[styles.genderOptionText, selectedGender === 'unisex' && styles.selectedGenderOptionText]}>
                  Unisex
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Color Selector */}
          <View style={styles.colorSection}>
            <ColorSelector
              selectedColors={selectedColors}
              onColorsChange={setSelectedColors}
              disabled={loading || uploading}
              style={styles.colorSelector}
            />
          </View>

          {/* Tags Input */}
          <Text style={styles.label}>Tags (Optional)</Text>          <TextInput
            style={styles.input}
            placeholder="e.g., cotton, summer, vintage (separate with commas)"
            value={tags}
            onChangeText={setTags}
            editable={!loading && !uploading}
            maxLength={200}
            placeholderTextColor="#000"
          />

          {/* Add Custom Category Modal */}
          <Modal
            visible={showAddCategoryModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowAddCategoryModal(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Add Custom Category</Text>                <TextInput
                  style={styles.modalInput}
                  placeholder="Enter category name"
                  value={newCategoryName}
                  onChangeText={setNewCategoryName}
                  autoCapitalize="words"
                  maxLength={20}
                  editable={!addingCategory}
                  placeholderTextColor="#000"
                />

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => {
                      setNewCategoryName('');
                      setShowAddCategoryModal(false);
                    }}
                    disabled={addingCategory}
                  >
                    <Text style={styles.modalCancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalAddButton, addingCategory && styles.modalAddButtonDisabled]}
                    onPress={addCustomCategory}
                    disabled={addingCategory || !newCategoryName.trim()}
                  >
                    {addingCategory ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Text style={styles.modalAddButtonText}>Add</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Image Picker Modal */}
          <Modal
            visible={showImagePickerModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowImagePickerModal(false)}
          >
            <View style={styles.imagePickerModalOverlay}>
              <View style={styles.imagePickerModalContent}>
                <View style={styles.imagePickerHeader}>
                  <Text style={styles.imagePickerTitle}>Add Images</Text>
                  <TouchableOpacity
                    onPress={() => setShowImagePickerModal(false)}
                    style={styles.imagePickerCloseButton}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.imagePickerSubtitle}>
                  Choose how you want to add images to your listing
                </Text>

                <View style={styles.imagePickerOptions}>
                  <TouchableOpacity
                    style={styles.imagePickerOption}
                    onPress={pickImageFromLibrary}
                    activeOpacity={0.7}
                  >
                    <View style={styles.imagePickerOptionIcon}>
                      <Ionicons name="images" size={32} color="#FF6B6B" />
                    </View>
                    <Text style={styles.imagePickerOptionTitle}>Photo Library</Text>
                    <Text style={styles.imagePickerOptionSubtitle}>
                      Select multiple photos from your gallery
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.imagePickerOption}
                    onPress={pickImageFromCamera}
                    activeOpacity={0.7}
                  >
                    <View style={styles.imagePickerOptionIcon}>
                      <Ionicons name="camera" size={32} color="#FF6B6B" />
                    </View>
                    <Text style={styles.imagePickerOptionTitle}>Camera</Text>
                    <Text style={styles.imagePickerOptionSubtitle}>
                      Take a new photo with your camera
                    </Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.imagePickerCancelButton}
                  onPress={() => setShowImagePickerModal(false)}
                >
                  <Text style={styles.imagePickerCancelText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>

          <Text style={styles.label}>Title</Text>          <TextInput
            style={styles.input}
            placeholder="Give your item a title"
            value={title}
            onChangeText={setTitle}
            editable={!loading && !uploading}
            maxLength={50}
            placeholderTextColor="#000"
          />

          <Text style={styles.label}>Description</Text>          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Describe your item (condition, features, etc.)"
            value={description}
            onChangeText={setDescription}
            editable={!loading && !uploading}
            multiline={true}
            numberOfLines={4}
            textAlignVertical="top"
            maxLength={500}
            placeholderTextColor="#000"
          />

          <Text style={styles.label}>Brand</Text>          <TextInput
            style={styles.input}
            placeholder="e.g., Nike, Zara"
            value={brand}
            onChangeText={setBrand}
            editable={!loading && !uploading}
            placeholderTextColor="#000"
          />

          <Text style={styles.label}>Size</Text>          <TextInput
            style={styles.input}
            placeholder="e.g., M, UK 10, 42"
            value={size}
            onChangeText={setSize}
            editable={!loading && !uploading}
            placeholderTextColor="#000"
          />
          <Text style={styles.label}>Buy Link (Optional)</Text>          <TextInput
            style={styles.input}
            placeholder="https://example.com/product-link (optional)"
            value={buyLink}
            onChangeText={setBuyLink}
            keyboardType="url"
            autoCapitalize="none"
            editable={!loading && !uploading}
            placeholderTextColor="#000"
          />

          <Text style={styles.label}>Price</Text>
          <View style={styles.priceInputContainer}>
            <Text style={styles.currencySymbol}>₹</Text>            <TextInput
              style={styles.priceInput}
              placeholder="Enter price"
              value={price}
              onChangeText={setPrice}
              keyboardType="numeric"
              editable={!loading && !uploading}
              placeholderTextColor="#000"
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, (loading || uploading) && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading || uploading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.submitButtonText}>Submit Item</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  notSellerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notSellerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  notSellerText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  becomeSellerButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  becomeSellerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  imageSection: {
    marginVertical: 20,
    marginHorizontal: 20,
  },
  imagePicker: {
    height: 250,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  smallImagePicker: {
    height: 200,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    alignItems: 'center',
  },
  imagePlaceholderText: {
    color: '#aaa',
    marginTop: 10,
    fontSize: 16,
  },
  imagePlaceholderSubText: {
    color: '#aaa',
    fontSize: 12,
    marginTop: 5,
  },
  imageCountBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  imageCountText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  thumbnailsContainer: {
    marginTop: 10,
    maxHeight: 90,
  },
  thumbnailsContent: {
    paddingRight: 10,
  },
  thumbnailWrapper: {
    width: 80,
    height: 80,
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  thumbnailTouch: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedThumbnail: {
    borderWidth: 2,
    borderColor: '#FF6B6B',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  removeThumbnailButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 12,
  },
  addMoreImagesButton: {
    width: 80,
    height: 80,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMoreImagesText: {
    color: '#FF6B6B',
    fontSize: 12,
    marginTop: 5,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 20,
    marginBottom: 10,
  },

  categorySection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  categorySelector: {
    // Additional styling if needed
  },
  genderSection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  genderOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    padding: 10,
  },
  genderOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  selectedGenderOption: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  genderOptionText: {
    color: '#555',
    fontWeight: 'bold',
  },
  selectedGenderOptionText: {
    color: '#fff',
  },
  colorSection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  colorSelector: {
    // Additional styling if needed
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 10,
  },
  categorySubLabel: {
    fontSize: 14,
    color: '#666',
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 5,
  },
  addCategoryText: {
    color: '#FF6B6B',
    marginLeft: 5,
    fontWeight: '500',
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginHorizontal: 15,
    marginBottom: 20,
  },
  categoryButtonWrapper: {
    position: 'relative',
    margin: 5,
  },
  categoryButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 20,
  },
  categoryButtonSelected: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  categoryButtonText: {
    color: '#555',
    fontWeight: 'bold',
  },
  categoryButtonTextSelected: {
    color: '#fff',
  },
  categoryDeleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '80%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  modalInput: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#eee',
    width: '100%',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalCancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#ccc',
    marginRight: 10,
    flex: 1,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  modalAddButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    flex: 1,
    alignItems: 'center',
  },
  modalAddButtonDisabled: {
    backgroundColor: '#ffb5b5',
  },
  modalAddButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  input: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#eee',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    marginHorizontal: 20,
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#333',
    marginRight: 5,
  },
  priceInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 40,
  },
  submitButtonDisabled: {
    backgroundColor: '#ffbaba',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Image Picker Modal Styles
  imagePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  imagePickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    paddingHorizontal: 20,
    minHeight: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imagePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  imagePickerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  imagePickerCloseButton: {
    padding: 5,
  },
  imagePickerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  imagePickerOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 30,
  },
  imagePickerOption: {
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    backgroundColor: '#f8f8f8',
    width: '45%',
    borderWidth: 1,
    borderColor: '#eee',
  },
  imagePickerOptionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  imagePickerOptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  imagePickerOptionSubtitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  imagePickerCancelButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 25,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  imagePickerCancelText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UploadScreen;