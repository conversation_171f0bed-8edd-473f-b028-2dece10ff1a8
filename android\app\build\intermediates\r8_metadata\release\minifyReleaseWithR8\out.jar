{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": false, "isRuntimeInvisibleParameterAnnotationsKept": false, "isRuntimeInvisibleTypeAnnotationsKept": false, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": false, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": false, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "24", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 41649076000, "numberOfThreads": 8}, "dexFiles": [{"checksum": "bb4c0f9dbc882282c31035511b7d526a3d26d66bbabb69935601d70fb93413c9", "startup": false}, {"checksum": "32aa689a276b9b1fd7aae414e99f91d19e8d722f5f6c895c845027fbaa506d7e", "startup": false}], "stats": {"noObfuscationPercentage": 44.46, "noOptimizationPercentage": 100.0, "noShrinkingPercentage": 44.73}, "version": "8.8.34"}