<libraries>
  <library
      name=":@@:react-native-google-signin_google-signin::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ef371daf9ca0827ab9c089862c94f5\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ef371daf9ca0827ab9c089862c94f5\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-google-signin_google-signin:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ef371daf9ca0827ab9c089862c94f5\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-screens::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7f676e3750c901af51d12f1388b26af\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\d7f676e3750c901af51d12f1388b26af\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-screens:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7f676e3750c901af51d12f1388b26af\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:react-android:0.79.2:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.79.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd954f90d8f2c4273306f49664d2061\transformed\fresco-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd954f90d8f2c4273306f49664d2061\transformed\fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3bef216feedfe7a33c874b14fbc22432\transformed\imagepipeline-okhttp3-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3bef216feedfe7a33c874b14fbc22432\transformed\imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\446f3c9390a97aac50fe98270f678820\transformed\middleware-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\446f3c9390a97aac50fe98270f678820\transformed\middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c157f06c0f96b582066a5b9eb599959\transformed\ui-common-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c157f06c0f96b582066a5b9eb599959\transformed\ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a026ff20cf35f01406db4a004942b9e\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\0a026ff20cf35f01406db4a004942b9e\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo:53.0.9"
      partialResultsDir="D:\app\StyleApp\node_modules\expo\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a026ff20cf35f01406db4a004942b9e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-file-system::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0764cb9473765ffe9fcd0b485ecfbeb7\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\0764cb9473765ffe9fcd0b485ecfbeb7\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-file-system:18.1.10"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0764cb9473765ffe9fcd0b485ecfbeb7\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name=":@@:expo-modules-core::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\445a24bd7b07b2abb0c058c08a776bac\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\445a24bd7b07b2abb0c058c08a776bac\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-modules-core:2.3.13"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\445a24bd7b07b2abb0c058c08a776bac\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-launcher::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e90045ba4d221bde4bba67905ec7f41d\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\e90045ba4d221bde4bba67905ec7f41d\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-launcher:5.1.11"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e90045ba4d221bde4bba67905ec7f41d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-menu::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b0cbe9180ed19f9be0582a598726369b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\b0cbe9180ed19f9be0582a598726369b\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu:6.1.10"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b0cbe9180ed19f9be0582a598726369b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\60b9f2d453f2a1c09171c19bd0ac70a8\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\60b9f2d453f2a1c09171c19bd0ac70a8\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-image-loader::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7879a949e133e3e3027ea983896d87\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7879a949e133e3e3027ea983896d87\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-image-loader:5.1.0"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7879a949e133e3e3027ea983896d87\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e01052569e02ab0291c0f03331c929c\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e01052569e02ab0291c0f03331c929c\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.webbrowser:14.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.webbrowser:14.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee50b4de2b12b944cfbb2daa7bde582\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee50b4de2b12b944cfbb2daa7bde582\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb85cff1fb455cfeb853e7d28becd57\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb85cff1fb455cfeb853e7d28becd57\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e263a4dd960e4588d23a27736a1b7b15\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e263a4dd960e4588d23a27736a1b7b15\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2da426d8ee806ec16571263a538041fb\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2da426d8ee806ec16571263a538041fb\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e62c7fb3c64d75ccde48e1655bf5c0\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e62c7fb3c64d75ccde48e1655bf5c0\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\734ae7c8352c0532597a0ebf552cab00\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\734ae7c8352c0532597a0ebf552cab00\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9657e101c69ac8a3aaf5e8c2b267790\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9657e101c69ac8a3aaf5e8c2b267790\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.1\bb0e192bd7c2b6b8217440d36e9758e377e450\kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.8.1\73e2acdd18df99dd4849d99f188dff529fc0afe0\kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.insert-koin:koin-core-jvm:3.5.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\3.5.6\71c101744c62708690796cdb48ed8522a74687c7\koin-core-jvm-3.5.6.jar"
      resolved="io.insert-koin:koin-core-jvm:3.5.6"/>
  <library
      name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrent-collections-jvm\2.0.6\fb80df9c69dd0e154c346ee5510601e2d148e23d\stately-concurrent-collections-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrent-collections-jvm:2.0.6"/>
  <library
      name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrency-jvm\2.0.6\14dcbce3fc3d80a5a07f9df33dd2dc54e437e8d0\stately-concurrency-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrency-jvm:2.0.6"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name=":@@:expo-constants::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b45029f3077fba102151f3e8792967\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b45029f3077fba102151f3e8792967\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-constants:17.1.6"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b45029f3077fba102151f3e8792967\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-image-manipulator::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3c56e4b456323bcd99f72f20557b8c\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3c56e4b456323bcd99f72f20557b8c\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-image-manipulator:13.1.7"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3c56e4b456323bcd99f72f20557b8c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-client::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\192e683cd48ebf9504166e2b86a147e6\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\192e683cd48ebf9504166e2b86a147e6\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-client:5.1.8"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\192e683cd48ebf9504166e2b86a147e6\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9145e54b32f16f48e4f4ff452aef51f4\transformed\viewbinding-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9145e54b32f16f48e4f4ff452aef51f4\transformed\viewbinding-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c13d5e1dba0c5c90d48382ed009c4dd6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c13d5e1dba0c5c90d48382ed009c4dd6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb7d108cd815b5dd21203251df132f2\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb7d108cd815b5dd21203251df132f2\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f398d430bfa01060ee6fa697657c61a7\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f398d430bfa01060ee6fa697657c61a7\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b3fb91b5227b84238b680dfb7e7bd3\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b3fb91b5227b84238b680dfb7e7bd3\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7469a7f8a668e564f27933100b711f8f\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7469a7f8a668e564f27933100b711f8f\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2a7ac7e8c682bcd03d2252707759078a\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2a7ac7e8c682bcd03d2252707759078a\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9097b48713004c2258b0ee96e36ec8d\transformed\ui-core-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9097b48713004c2258b0ee96e36ec8d\transformed\ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d45e378629d6a6ab396b8e8b880ed6a5\transformed\imagepipeline-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d45e378629d6a6ab396b8e8b880ed6a5\transformed\imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f0ae2db523f9adde7bccdd47cf308816\transformed\imagepipeline-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f0ae2db523f9adde7bccdd47cf308816\transformed\imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.21\222b2be42672d47c002c1b22ac9f030d781fc5db\kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d0b5d7964179a877ced792399dce87fb\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\d0b5d7964179a877ced792399dce87fb\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-async-storage_async-storage:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d0b5d7964179a877ced792399dce87fb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-picker_picker::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\558a3e9a370102a9cafe3c62e00ba161\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\558a3e9a370102a9cafe3c62e00ba161\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-picker_picker:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\558a3e9a370102a9cafe3c62e00ba161\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:lottie-react-native::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6404c897c40182cec9b2be47b14bb483\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\6404c897c40182cec9b2be47b14bb483\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:lottie-react-native:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6404c897c40182cec9b2be47b14bb483\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-fast-image::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d03307d9638de6ff4a751786be0ff0d7\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\d03307d9638de6ff4a751786be0ff0d7\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-fast-image:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d03307d9638de6ff4a751786be0ff0d7\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\32333349f43ab51941ab90546a4778f2\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\32333349f43ab51941ab90546a4778f2\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-gesture-handler:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\32333349f43ab51941ab90546a4778f2\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-razorpay::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9364af7ccf2e0081dd3341ffe164be2\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\c9364af7ccf2e0081dd3341ffe164be2\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-razorpay:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9364af7ccf2e0081dd3341ffe164be2\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-reanimated::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\551ca694467f9edc785ffaa989bfcae9\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\551ca694467f9edc785ffaa989bfcae9\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-reanimated:3.17.5"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\551ca694467f9edc785ffaa989bfcae9\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2a286dc2f212a3648e6faa89bab75934\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\2a286dc2f212a3648e6faa89bab75934\transformed\out\jars\libs\R.jar"
      resolved="SwipeSense:react-native-safe-area-context:unspecified"
      partialResultsDir="D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2a286dc2f212a3648e6faa89bab75934\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-gif:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e37d945e8cdd593d153ae6b326930c29\transformed\animated-gif-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e37d945e8cdd593d153ae6b326930c29\transformed\animated-gif-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\92ac8bc596cbb547332dca89c376d1c0\transformed\webpsupport-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\92ac8bc596cbb547332dca89c376d1c0\transformed\webpsupport-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.79.2:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45535a49377975f245c627db7f8fd4b9\transformed\hermes-android-0.79.2-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.79.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45535a49377975f245c627db7f8fd4b9\transformed\hermes-android-0.79.2-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4bccd564c4f2a425ba7527fe4eb35e1f\transformed\fbjni-0.7.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4bccd564c4f2a425ba7527fe4eb35e1f\transformed\fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2580c5f00df3cbf3046df8e7aa72b729\transformed\imagepipeline-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2580c5f00df3cbf3046df8e7aa72b729\transformed\imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b1677671948d60be2dc241555c23bb2\transformed\memory-type-ashmem-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b1677671948d60be2dc241555c23bb2\transformed\memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8162ad386fe5e0976b51bde6cd6752c9\transformed\memory-type-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8162ad386fe5e0976b51bde6cd6752c9\transformed\memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\86df225c801f04dd83f966fb7199d290\transformed\memory-type-java-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\86df225c801f04dd83f966fb7199d290\transformed\memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\db61630674b5354a7461fad1c3e11e92\transformed\nativeimagefilters-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\db61630674b5354a7461fad1c3e11e92\transformed\nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\58bc3c894ad84d2b65f15f4a016489f4\transformed\nativeimagetranscoder-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\58bc3c894ad84d2b65f15f4a016489f4\transformed\nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3d61080a10e48df79fafacf9da4e70d8\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3d61080a10e48df79fafacf9da4e70d8\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.application:6.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcbc3b5eb86a21cac52935b2937caaa\transformed\expo.modules.application-6.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.application:6.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcbc3b5eb86a21cac52935b2937caaa\transformed\expo.modules.application-6.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="expo.modules.asset:expo.modules.asset:11.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c7871d9d24c3f3746bb4a5fc40278656\transformed\expo.modules.asset-11.1.5\jars\classes.jar"
      resolved="expo.modules.asset:expo.modules.asset:11.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c7871d9d24c3f3746bb4a5fc40278656\transformed\expo.modules.asset-11.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.crypto:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\928079e25932fbb6a7c41340fa8db348\transformed\expo.modules.crypto-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.crypto:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\928079e25932fbb6a7c41340fa8db348\transformed\expo.modules.crypto-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.font:13.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b80d12792d1a7be2256f4e37b58b60ad\transformed\expo.modules.font-13.3.1\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.font:13.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b80d12792d1a7be2256f4e37b58b60ad\transformed\expo.modules.font-13.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.imagepicker:16.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.imagepicker:16.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d234a62f821b30da3491134936d98eee\transformed\expo.modules.keepawake-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.keepawake:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d234a62f821b30da3491134936d98eee\transformed\expo.modules.keepawake-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.lineargradient:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\14367743b3d448efc6a1091e95859271\transformed\expo.modules.lineargradient-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.lineargradient:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\14367743b3d448efc6a1091e95859271\transformed\expo.modules.lineargradient-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-menu-interface::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\55dcf217f74bcef4af2f40ebf9f1de0d\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\55dcf217f74bcef4af2f40ebf9f1de0d\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu-interface:1.10.0"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\55dcf217f74bcef4af2f40ebf9f1de0d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-eas-client::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d868250f4c4675ae6c8f3d70299141b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\1d868250f4c4675ae6c8f3d70299141b\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-eas-client:0.14.3"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d868250f4c4675ae6c8f3d70299141b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-json-utils::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cc29bfa6392ce08b25a0f092e41a32d4\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\cc29bfa6392ce08b25a0f092e41a32d4\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-json-utils:0.15.0"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cc29bfa6392ce08b25a0f092e41a32d4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-linking::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e58739aa088cfd6389c37ac61d0d842\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\7e58739aa088cfd6389c37ac61d0d842\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-linking:7.1.5"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e58739aa088cfd6389c37ac61d0d842\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-manifests::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2abbb1eaa303ec01f3c67fe73634284d\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\2abbb1eaa303ec01f3c67fe73634284d\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-manifests:0.16.5"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2abbb1eaa303ec01f3c67fe73634284d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-notifications::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d1cfc50d3842bb41de1a12059e37b982\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\d1cfc50d3842bb41de1a12059e37b982\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-notifications:0.31.2"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d1cfc50d3842bb41de1a12059e37b982\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-structured-headers::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\22979f927e3b48ca476c02038271573b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\22979f927e3b48ca476c02038271573b\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-structured-headers:4.1.0"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\22979f927e3b48ca476c02038271573b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-updates::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\656a05994cbb349ad6135b5ef3dd15e5\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\656a05994cbb349ad6135b5ef3dd15e5\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-updates:0.28.13"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\656a05994cbb349ad6135b5ef3dd15e5\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-updates-interface::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2429b72542031b26021c1c6e79b993\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2429b72542031b26021c1c6e79b993\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-updates-interface:1.1.0"
      partialResultsDir="D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2429b72542031b26021c1c6e79b993\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\jars\classes.jar"
      resolved="com.google.android.material:material:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.vanniktech:android-image-cropper:4.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\jars\classes.jar"
      resolved="com.vanniktech:android-image-cropper:4.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:checkout:1.6.41@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\jars\classes.jar"
      resolved="com.razorpay:checkout:1.6.41"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:standard-core:1.6.51@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\jars\classes.jar"
      resolved="com.razorpay:standard-core:1.6.51"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-wallet:18.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3\jars\classes.jar"
      resolved="com.google.android.gms:play-services-wallet:18.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aa7089c0ee765e01549bc3392fb0006c\transformed\play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aa7089c0ee765e01549bc3392fb0006c\transformed\play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-identity:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-identity:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-maps:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-maps:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e22240d77ee0f4d85702fcf824e14a5\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e22240d77ee0f4d85702fcf824e14a5\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0234a2f446045858446afb54e99c4f6\transformed\animated-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0234a2f446045858446afb54e99c4f6\transformed\animated-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f718ba2aef815ea123dd94a0b7b11a6b\transformed\animated-drawable-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f718ba2aef815ea123dd94a0b7b11a6b\transformed\animated-drawable-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\08b4a880772cc9db84cee2b1e2b17462\transformed\vito-options-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\08b4a880772cc9db84cee2b1e2b17462\transformed\vito-options-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ba28e1a6fd30e6187724d2984890b19\transformed\urimod-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ba28e1a6fd30e6187724d2984890b19\transformed\urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d955b3d60156daa82f0383953fd510\transformed\vito-source-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d955b3d60156daa82f0383953fd510\transformed\vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2cb820fc5db64a7525eeb2682ddcf720\transformed\soloader-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2cb820fc5db64a7525eeb2682ddcf720\transformed\soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9f09bb2d5dc35eb4e928e272f6dca30c\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9f09bb2d5dc35eb4e928e272f6dca30c\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.6.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.8.1\71eb0cace3aaa93591a613a32c92853c464d2a53\kotlinx-coroutines-play-services-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-brotli:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-brotli\4.9.2\6cfe9555a20f39ca88be72b5c70147f20520e761\okhttp-brotli-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-brotli:4.9.2"/>
  <library
      name="co.touchlab:stately-strict-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-strict-jvm\2.0.6\fdbcb1fc1c9219aa5a5f2b1c9084a4ed8b2a8f8b\stately-strict-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-strict-jvm:2.0.6"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\2.0.21\669e1d35e4ca1797f9ddb2830dd6c36c0ca531e4\kotlin-reflect-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.0.21"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.0.21\8947a70750a8552acfe84ad5703e16bf072d9368\kotlin-parcelize-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.0.21\ec0f8b769af81cd91a76b530399684a9567bdc35\kotlin-android-extensions-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed01b3e70387f3ba7d6a0aee0352e49\transformed\vito-renderer-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed01b3e70387f3ba7d6a0aee0352e49\transformed\vito-renderer-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.code.gson:gson:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.6\9180733b7df8542621dc12e21e87557e8c99b8cb\gson-2.8.6.jar"
      resolved="com.google.code.gson:gson:2.8.6"/>
  <library
      name="me.leolin:ShortcutBadger:1.1.22@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\jars\classes.jar"
      resolved="me.leolin:ShortcutBadger:1.1.22"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.bouncycastle:bcutil-jdk15to18:1.78.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.bouncycastle\bcutil-jdk15to18\1.78.1\4853966f92545c988dcc8ce017344171fdf371cd\bcutil-jdk15to18-1.78.1.jar"
      resolved="org.bouncycastle:bcutil-jdk15to18:1.78.1"/>
  <library
      name="com.android.installreferrer:installreferrer:2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:2.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="org.brotli:dec:0.1.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.brotli\dec\0.1.2\c26a897ae0d524809eef1c786cc6183b4ddcc3b\dec-0.1.2.jar"
      resolved="org.brotli:dec:0.1.2"/>
  <library
      name="org.bouncycastle:bcprov-jdk15to18:1.78.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.bouncycastle\bcprov-jdk15to18\1.78.1\83bfa8229f7127d933161aefb281e54a9ffcf9f4\bcprov-jdk15to18-1.78.1.jar"
      resolved="org.bouncycastle:bcprov-jdk15to18:1.78.1"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
</libraries>
