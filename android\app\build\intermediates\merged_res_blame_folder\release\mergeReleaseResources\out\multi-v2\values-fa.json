{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4216,4324,4481,4610,4713,4853,4979,5093,5350,5503,5613,5783,5914,6067,6223,6289,6357", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "4319,4476,4605,4708,4848,4974,5088,5190,5498,5608,5778,5909,6062,6218,6284,6352,6437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10b099e99495a469d20423a3ae446b36\\transformed\\android-image-cropper-4.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,117,167,243,318,391,443", "endColumns": "61,49,75,74,72,51,72", "endOffsets": "112,162,238,313,386,438,511"}, "to": {"startLines": "65,72,73,74,75,76,131", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6541,7139,7189,7265,7340,7413,11609", "endColumns": "61,49,75,74,72,51,72", "endOffsets": "6598,7184,7260,7335,7408,7460,11677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,374,475,586,670,771,886,966,1043,1136,1231,1323,1417,1519,1614,1711,1805,1898,1988,2070,2178,2282,2380,2486,2591,2696,2853,11992", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "369,470,581,665,766,881,961,1038,1131,1226,1318,1412,1514,1609,1706,1800,1893,1983,2065,2173,2277,2375,2481,2586,2691,2848,2949,12069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "69", "endOffsets": "271"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "13096", "endColumns": "73", "endOffsets": "13165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "64,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6442,6756,6853,6964", "endColumns": "98,96,110,102", "endOffsets": "6536,6848,6959,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "35,36,37,38,39,40,41,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3105,3204,3306,3405,3505,3606,3712,12700", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3199,3301,3400,3500,3601,3707,3824,12796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45da6d36d3d2ef1e836ae57a6da35b9a\\transformed\\react-android-0.79.2-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,273,342,423,491,557,631,706,787,868,937,1016,1094,1168,1250,1331,1410,1483,1554,1642,1713,1789,1861", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,196,268,337,418,486,552,626,701,782,863,932,1011,1089,1163,1245,1326,1405,1478,1549,1637,1708,1784,1856,1932"}, "to": {"startLines": "33,45,71,78,79,81,94,95,96,132,133,134,135,137,138,139,140,141,142,143,144,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2954,4139,7067,7534,7603,7744,8716,8782,8856,11682,11763,11844,11913,12074,12152,12226,12308,12389,12468,12541,12612,12801,12872,12948,13020", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "3018,4211,7134,7598,7679,7807,8777,8851,8926,11758,11839,11908,11987,12147,12221,12303,12384,12463,12536,12607,12695,12867,12943,13015,13091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5195", "endColumns": "154", "endOffsets": "5345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,296,393,522,606,669,759,828,888,979,1043,1102,1169,1231,1286,1409,1467,1528,1583,1655,1792,1873,1955,2055,2129,2203,2289,2356,2422,2493,2570,2651,2724,2798,2868,2942,3028,3102,3191,3283,3357,3430,3519,3570,3637,3720,3804,3866,3930,3993,4087,4194,4287,4392", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "209,291,388,517,601,664,754,823,883,974,1038,1097,1164,1226,1281,1404,1462,1523,1578,1650,1787,1868,1950,2050,2124,2198,2284,2351,2417,2488,2565,2646,2719,2793,2863,2937,3023,3097,3186,3278,3352,3425,3514,3565,3632,3715,3799,3861,3925,3988,4082,4189,4282,4387,4465"}, "to": {"startLines": "2,34,42,43,44,66,67,77,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3023,3829,3926,4055,6603,6666,7465,7684,7812,7903,7967,8026,8093,8155,8210,8333,8391,8452,8507,8579,8931,9012,9094,9194,9268,9342,9428,9495,9561,9632,9709,9790,9863,9937,10007,10081,10167,10241,10330,10422,10496,10569,10658,10709,10776,10859,10943,11005,11069,11132,11226,11333,11426,11531", "endLines": "5,34,42,43,44,66,67,77,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "259,3100,3921,4050,4134,6661,6751,7529,7739,7898,7962,8021,8088,8150,8205,8328,8386,8447,8502,8574,8711,9007,9089,9189,9263,9337,9423,9490,9556,9627,9704,9785,9858,9932,10002,10076,10162,10236,10325,10417,10491,10564,10653,10704,10771,10854,10938,11000,11064,11127,11221,11328,11421,11526,11604"}}]}]}