import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  StatusBar,
  Dimensions,
  RefreshControl
} from 'react-native';
import { collection, query, where, getDocs, orderBy, limit, doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.7;
const CARD_HEIGHT = CARD_WIDTH * 1.2;

const SellerMetricsScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [mostLikedItems, setMostLikedItems] = useState([]);
  const [mostCommentedItems, setMostCommentedItems] = useState([]);
  const [mostViewedItems, setMostViewedItems] = useState([]);
  const [mostSavedItems, setMostSavedItems] = useState([]);
  const [mostSwipedItems, setMostSwipedItems] = useState([]);
  const [mostOrderedItems, setMostOrderedItems] = useState([]);
  const [totalStats, setTotalStats] = useState({
    totalItems: 0,
    totalLikes: 0,
    totalComments: 0,
    totalViews: 0,
    totalSaves: 0,
    totalSwipeViews: 0,
    totalOrders: 0
  });

  const currentUserId = auth.currentUser?.uid;

  // Fetch all metrics data
  const fetchMetricsData = useCallback(async () => {
    if (!currentUserId) return;

    setLoading(true);
    try {
      // Base query for user's items
      const baseQuery = query(
        collection(db, 'clothingItems'),
        where('userId', '==', currentUserId)
      );

      // Get total items count
      const totalItemsSnapshot = await getDocs(baseQuery);
      const totalItems = totalItemsSnapshot.size;

      // Calculate total stats
      let likes = 0;
      let comments = 0;
      let views = 0;
      let saves = 0;
      let swipeViews = 0;
      let totalOrders = 0;

      totalItemsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        likes += data.likeCount || 0;
        comments += data.commentCount || 0;
        views += data.viewCount || 0;
        saves += data.saveCount || 0;
        swipeViews += data.swipeViewCount || 0;
        totalOrders += data.orderCount || 0;
      });

      setTotalStats({
        totalItems,
        totalLikes: likes,
        totalComments: comments,
        totalViews: views,
        totalSaves: saves,
        totalSwipeViews: swipeViews,
        totalOrders: totalOrders
      });

      // Fetch most liked items
      const likedQuery = query(
        baseQuery,
        orderBy('likeCount', 'desc'),
        limit(10)
      );
      const likedSnapshot = await getDocs(likedQuery);
      setMostLikedItems(likedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      // Fetch most commented items
      const commentedQuery = query(
        baseQuery,
        orderBy('commentCount', 'desc'),
        limit(10)
      );
      const commentedSnapshot = await getDocs(commentedQuery);
      setMostCommentedItems(commentedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      // Fetch most viewed items (based on cart additions)
      const viewedQuery = query(
        baseQuery,
        orderBy('viewCount', 'desc'),
        limit(10)
      );
      const viewedSnapshot = await getDocs(viewedQuery);
      setMostViewedItems(viewedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      // Fetch most saved items
      const savedQuery = query(
        baseQuery,
        orderBy('saveCount', 'desc'),
        limit(10)
      );
      const savedSnapshot = await getDocs(savedQuery);
      setMostSavedItems(savedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      // Fetch most swiped items
      const swipedQuery = query(
        baseQuery,
        orderBy('swipeViewCount', 'desc'),
        limit(10)
      );
      const swipedSnapshot = await getDocs(swipedQuery);
      setMostSwipedItems(swipedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      // Fetch most ordered items
      const orderedQuery = query(
        baseQuery,
        orderBy('orderCount', 'desc'),
        limit(10)
      );
      const orderedSnapshot = await getDocs(orderedQuery);
      setMostOrderedItems(orderedSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));

    } catch (error) {
      console.error('Error fetching metrics data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [currentUserId]);

  // Use useFocusEffect to fetch data when the screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchMetricsData();
    }, [fetchMetricsData])
  );

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchMetricsData();
  }, [fetchMetricsData]);

  // Render a horizontal list of items for each metric
  const renderItemList = (items, icon, color, metric) => {
    if (items.length === 0) {
      return (
        <View style={styles.emptyListContainer}>
          <Ionicons name="alert-circle-outline" size={40} color="#ccc" />
          <Text style={styles.emptyText}>No data available</Text>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.horizontalList}
      >
        {items.map((item, index) => (
          <TouchableOpacity
            key={item.id}
            style={styles.itemCard}
            onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
          >
            <Image source={{ uri: item.imageUrl }} style={styles.itemImage} />
            <View style={styles.itemOverlay}>
              <View style={styles.rankBadge}>
                <Text style={styles.rankText}>{index + 1}</Text>
              </View>
              <View style={styles.metricBadge} backgroundColor={color}>
                <Ionicons name={icon} size={14} color="#fff" />
                <Text style={styles.metricText}>{item[metric] || 0}</Text>
              </View>
            </View>
            <View style={styles.itemInfo}>
              <Text style={styles.itemTitle} numberOfLines={1}>{item.title || 'Untitled'}</Text>
              <Text style={styles.itemCategory}>{item.category}</Text>
              {item.gender && <Text style={styles.itemGender}>{item.gender}</Text>}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  // Render a stat card
  const renderStatCard = (title, value, icon, color) => (
    <View style={styles.statCard}>
      <View style={[styles.statIconContainer, { backgroundColor: color }]}>
        <Ionicons name={icon} size={24} color="#fff" />
      </View>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statTitle}>{title}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Metrics</Text>
        </View>

        {loading && !refreshing ? (
          <ActivityIndicator style={styles.loader} size="large" color="#FF6B6B" />
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={["#FF6B6B"]} />
            }
          >
            {/* Stats Overview */}
            <View style={styles.statsContainer}>
              {renderStatCard('Items', totalStats.totalItems, 'shirt-outline', '#FF6B6B')}
              {renderStatCard('Likes', totalStats.totalLikes, 'heart-outline', '#FF4081')}
              {renderStatCard('Views', totalStats.totalSwipeViews, 'eye-outline', '#00BCD4')}
              {renderStatCard('Comments', totalStats.totalComments, 'chatbubble-outline', '#4A90E2')}
              {renderStatCard('Saves', totalStats.totalSaves, 'bookmark-outline', '#7C4DFF')}
              {renderStatCard('Orders', totalStats.totalOrders, 'bag-outline', '#4CAF50')}
              {renderStatCard('Cart Adds', totalStats.totalViews, 'cart-outline', '#FF6B6B')}
            </View>

            {/* Most Liked Items */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Liked</Text>
                <Ionicons name="heart" size={20} color="#FF4081" />
              </View>
              {renderItemList(mostLikedItems, 'heart', '#FF4081', 'likeCount')}
            </View>

            {/* Most Commented Items */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Trending</Text>
                <Ionicons name="chatbubble" size={20} color="#4A90E2" />
              </View>
              {renderItemList(mostCommentedItems, 'chatbubble', '#4A90E2', 'commentCount')}
            </View>

            {/* Most Viewed Items (based on swipes) */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Viewed</Text>
                <Ionicons name="eye" size={20} color="#00BCD4" />
              </View>
              {renderItemList(mostSwipedItems, 'eye', '#00BCD4', 'swipeViewCount')}
            </View>

            {/* Most Saved Items */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Saved</Text>
                <Ionicons name="bookmark" size={20} color="#7C4DFF" />
              </View>
              {renderItemList(mostSavedItems, 'bookmark', '#7C4DFF', 'saveCount')}
            </View>

            {/* Most Added to Cart */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Added to Cart</Text>
                <Ionicons name="cart" size={20} color="#FF6B6B" />
              </View>
              {renderItemList(mostViewedItems, 'cart', '#FF6B6B', 'viewCount')}
            </View>

            {/* Most Ordered Items */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Most Ordered</Text>
                <Ionicons name="bag" size={20} color="#4CAF50" />
              </View>
              {renderItemList(mostOrderedItems, 'bag', '#4CAF50', 'orderCount')}
            </View>

            {/* Bottom padding */}
            <View style={{ height: 30 }} />
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    paddingBottom: 15,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#f9f9f9',
  },
  statCard: {
    width: '28%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  sectionContainer: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  horizontalList: {
    paddingBottom: 16,
    paddingRight: 16,
  },
  itemCard: {
    width: CARD_WIDTH,
    marginRight: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  itemImage: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    backgroundColor: '#f0f0f0',
  },
  itemOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  rankBadge: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rankText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  metricBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
  },
  metricText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 4,
    fontSize: 14,
  },
  itemInfo: {
    padding: 12,
    backgroundColor: '#fff',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  itemCategory: {
    fontSize: 14,
    color: '#666',
  },
  itemGender: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginTop: 2,
  },
  emptyListContainer: {
    height: CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
  },
});

export default SellerMetricsScreen;
