import { useRef, useCallback, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  runOnJS,
  withTiming
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const ROTATION_ANGLE = 12;
const VISIBLE_CARDS = 3;
const CARD_SCALE_DECREMENT = 0.03;
const CARD_POSITION_OFFSET = 15;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // Animation values
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Card animations for stack effect
  const cardAnimations = useRef(
    Array(VISIBLE_CARDS).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - CARD_SCALE_DECREMENT * index),
      translateY: useSharedValue(CARD_POSITION_OFFSET * index),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Reset card animations immediately (for next card to appear fresh)
  const resetCardAnimations = useCallback(() => {
    try {
      // Immediately reset main card position without animation
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;

      // Reset underlying cards to their default positions
      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        cardAnimations[i].scale.value = 1 - CARD_SCALE_DECREMENT * (i + 1);
        cardAnimations[i].translateY.value = CARD_POSITION_OFFSET * (i + 1);
        cardAnimations[i].opacity.value = 1;
      }
    } catch (error) {
      console.error("[useSwipeGestures] Error in resetCardAnimations:", error);
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    }
  }, [translateX, translateY, rotate, cardAnimations]);

  // Process swipe completion
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Immediately reset animations for the next card to appear fresh
    resetCardAnimations();
  }, [onSwipeComplete, resetCardAnimations]);

  // Create pan gesture - recreate when currentIndex or items change
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      const startX = translateX.value;
      const startY = translateY.value;

      translateX.value = startX;
      translateY.value = startY;

      if (Platform.OS === 'android') {
        rotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Handle movement based on direction
      if (direction === 'horizontal') {
        translateX.value = translationX;
        translateY.value = 0;

        // Calculate rotation manually to avoid interpolate warnings
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        rotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        translateY.value = Math.min(0, translationY);
        translateX.value = 0;
      } else {
        translateY.value = 0;
        translateX.value = 0;
        return;
      }

      // Calculate animation progress for cards beneath
      let swipeProgress;
      if (direction === 'horizontal') {
        swipeProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.5), 1);
      } else if (direction === 'up') {
        swipeProgress = Math.min(Math.abs(translationY) / 100, 1);
      } else {
        swipeProgress = 0;
      }

      // Animate cards beneath with throttling to reduce warnings
      if (swipeProgress > 0.01) { // Only animate if there's meaningful progress
        for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
          const scaleFrom = 1 - CARD_SCALE_DECREMENT * (i + 1);
          const scaleTo = 1 - CARD_SCALE_DECREMENT * i;
          const translateFrom = CARD_POSITION_OFFSET * (i + 1);
          const translateTo = CARD_POSITION_OFFSET * i;

          cardAnimations[i].scale.value = scaleFrom + (scaleTo - scaleFrom) * swipeProgress;
          cardAnimations[i].translateY.value = translateFrom + (translateTo - translateFrom) * swipeProgress;
          cardAnimations[i].opacity.value = 1;
        }
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[useSwipeGestures] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[useSwipeGestures] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        // Determine primary direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }        // Handle upward swipe
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[useSwipeGestures] Upward swipe detected');

          // Note: Cart animation will be triggered by addToCart in useCartManager
          // This prevents double animation triggers

          const upSwipeDuration = Platform.OS === 'android' ? 50 : 50;
          const targetY = -SCREEN_HEIGHT * 1.5;

          translateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              runOnJS(processSwipeCompletion)('up', swipedItem);
            } else {
              runOnJS(resetCardAnimations)();
            }
          });

          translateX.value = withTiming(0, { duration: Platform.OS === 'android' ? upSwipeDuration / 2 : upSwipeDuration });

          if (Platform.OS === 'android') {
            rotate.value = withTiming(0, { duration: upSwipeDuration / 2 });
          }

          return;
        }

        // Handle horizontal swipe
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5; const swipeDuration = Platform.OS === 'android' ? 50 : 50;
            const rotationDuration = Platform.OS === 'android' ? 50 : 50;
            const verticalDuration = Platform.OS === 'android' ? 50 : 50;

            translateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                runOnJS(processSwipeCompletion)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetCardAnimations)();
              }
            });

            rotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: rotationDuration });
            translateY.value = withTiming(0, { duration: verticalDuration });
            return;
          }
        }

        // No significant swipe detected
        console.log('[useSwipeGestures] No significant swipe detected. Resetting.');
        runOnJS(resetCardAnimations)();

      } catch (err) {
        console.error("[useSwipeGestures] Error in swipe onEnd:", err);
        runOnJS(resetCardAnimations)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetCardAnimations]);

  // Animated style for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations,
    cardAnimations
  };
};
