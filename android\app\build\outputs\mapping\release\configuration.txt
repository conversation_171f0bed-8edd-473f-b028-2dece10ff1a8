# The proguard configuration file for the following section is D:\app\StyleApp\android\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.canhub.cropper.CropFileProvider { <init>(); }
-keep class com.canhub.cropper.CropImageActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.razorpay.CheckoutActivity { <init>(); }
-keep class com.razorpay.MagicXActivity { <init>(); }
-keep class com.swipesense.swipesense.MainActivity { <init>(); }
-keep class com.swipesense.swipesense.MainApplication { <init>(); }
-keep class expo.modules.filesystem.FileSystemFileProvider { <init>(); }
-keep class expo.modules.imagepicker.fileprovider.ImagePickerFileProvider { <init>(); }
-keep class expo.modules.notifications.service.ExpoFirebaseMessagingService { <init>(); }
-keep class expo.modules.notifications.service.NotificationForwarderActivity { <init>(); }
-keep class expo.modules.notifications.service.NotificationsService { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.Barrier { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.Guideline { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager.widget.ViewPager { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.canhub.cropper.CropImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.canhub.cropper.CropOverlayView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.facebook.react.modules.dialog.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.ChipGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textview.MaterialTextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.razorpay.CircularProgressView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.reactnativecommunity.picker.CheckedTextViewImpl { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.reactnativecommunity.picker.TextViewImpl { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from D:\app\StyleApp\android\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is D:\app\StyleApp\android\app\build\intermediates\default_proguard_files\global\proguard-android.txt-8.8.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize steps (and performs some
# of these optimizations on its own).
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.
-dontoptimize

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\app\StyleApp\android\app\build\intermediates\default_proguard_files\global\proguard-android.txt-8.8.2
# The proguard configuration file for the following section is D:\app\StyleApp\android\app\proguard-rules.pro
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }

# Proguard rules for Razorpay and common issues
# Keep proguard.annotation.Keep and KeepClassMembers (fixes missing class error)
-keep class proguard.annotation.Keep { *; }
-keep class proguard.annotation.KeepClassMembers { *; }

# Razorpay SDK recommended rules
-keep class com.razorpay.** { *; }
-dontwarn com.razorpay.**

# React Native and Expo recommended rules
-keep class com.facebook.react.** { *; }
-dontwarn com.facebook.react.**
-keep class com.facebook.** { *; }
-dontwarn com.facebook.**
-keep class expo.modules.** { *; }
-dontwarn expo.modules.**

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep annotations
-keep @interface *

# Keep all class members annotated with @Keep
-keepclassmembers class * {
    @proguard.annotation.Keep <fields>;
    @proguard.annotation.Keep <methods>;
}

# Add any project specific keep options here:

# End of content from D:\app\StyleApp\android\app\proguard-rules.pro
# The proguard configuration file for the following section is D:\app\StyleApp\node_modules\expo\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# For ReactActivityDelegateWrapper
-keepclassmembers public class com.facebook.react.ReactActivityDelegate {
  public *;
  protected *;
  private ReactDelegate mReactDelegate;
}
# Remove this after react-native 0.74.1
-keepclassmembers public class expo.modules.ReactActivityDelegateWrapper {
  protected ReactDelegate getReactDelegate();
}
-keepclassmembers public class com.facebook.react.ReactActivity {
  private final ReactActivityDelegate mDelegate;
}

# For ReactNativeHostWrapper
-keepclassmembers public class com.facebook.react.ReactNativeHost {
  protected *;
}

# For ExpoModulesPackage autolinking
-keepclassmembers public class expo.modules.ExpoModulesPackageList {
  public *;
}

-keepnames class * extends expo.modules.core.BasePackage
-keepnames class * implements expo.modules.core.interfaces.Package

# End of content from D:\app\StyleApp\node_modules\expo\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }
-keep class com.facebook.react.fabric.** { *; }

# End of content from D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep @expo.modules.core.interfaces.DoNotStrip class *
-keepclassmembers class * {
  @expo.modules.core.interfaces.DoNotStrip *;
}

-keep class * implements expo.modules.kotlin.records.Record {
  *;
}
-keep class * extends expo.modules.kotlin.sharedobjects.SharedObject
-keep enum * implements expo.modules.kotlin.types.Enumerable {
  *;
}
-keepnames class kotlin.Pair

-keep,allowoptimization,allowobfuscation class * extends expo.modules.kotlin.modules.Module {
  public <init>();
  public expo.modules.kotlin.modules.ModuleDefinitionData definition();
}

-keepclassmembers class * implements expo.modules.kotlin.views.ExpoView {
  public <init>(android.content.Context);
  public <init>(android.content.Context, expo.modules.kotlin.AppContext);
}

-keepclassmembers class * {
  expo.modules.kotlin.viewevent.ViewEventCallback *;
}

-keepclassmembers class * {
  expo.modules.kotlin.viewevent.ViewEventDelegate *;
}

-keep class * implements expo.modules.kotlin.views.ComposeProps {
  *;
}

-keepnames class * implements expo.modules.kotlin.views.ExpoView {
  *;
}

# End of content from D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keepclassmembers class com.facebook.react.ReactInstanceManager {
  private final com.facebook.react.bridge.JSBundleLoader mBundleLoader;
}

-keepclassmembers class com.facebook.react.devsupport.ReleaseDevSupportManager {
  private final com.facebook.react.bridge.JSExceptionHandler defaultJSExceptionHandler;
}

# End of content from D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\proguard.txt
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# Disabling obfuscation is useful if you collect stack traces from production crashes
# (unless you are using a system that supports de-obfuscate the stack traces).
# -dontobfuscate

# React Native

# Keep our interfaces so they can be used by other ProGuard rules.
# See http://sourceforge.net/p/proguard/bugs/466/
-keep,allowobfuscation @interface com.facebook.proguard.annotations.DoNotStrip
-keep,allowobfuscation @interface com.facebook.proguard.annotations.KeepGettersAndSetters

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.proguard.annotations.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.proguard.annotations.DoNotStrip *;
}
-keep @com.facebook.proguard.annotations.DoNotStripAny class * {
    *;
}
-keep @com.facebook.jni.annotations.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.jni.annotations.DoNotStrip *;
}
-keep @com.facebook.jni.annotations.DoNotStripAny class * {
    *;
}

-keepclassmembers @com.facebook.proguard.annotations.KeepGettersAndSetters class * {
  void set*(***);
  *** get*();
}

-keep class * implements com.facebook.react.bridge.JavaScriptModule { *; }
-keep class * implements com.facebook.react.bridge.NativeModule { *; }
-keepclassmembers,includedescriptorclasses class * { native <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }

-dontwarn com.facebook.react.**
-keep,includedescriptorclasses class com.facebook.react.bridge.** { *; }
-keep,includedescriptorclasses class com.facebook.react.turbomodule.core.** { *; }
-keep,includedescriptorclasses class com.facebook.react.internal.turbomodule.core.** { *; }

# hermes
-keep class com.facebook.jni.** { *; }


# okio

-keep class sun.misc.Unsafe { *; }
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn okio.**

# yoga
-keep,allowobfuscation @interface com.facebook.yoga.annotations.DoNotStrip
-keep @com.facebook.yoga.annotations.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.yoga.annotations.DoNotStrip *;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\proguard.txt
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Uncomment for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzev {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**
-dontwarn androidx.lifecycle.LiveData

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\proguard.txt
# Keep our interfaces so they can be used by other ProGuard rules.
# See http://sourceforge.net/p/proguard/bugs/466/
-keep,allowobfuscation @interface com.facebook.common.internal.DoNotStrip
-keep,allowobfuscation @interface com.facebook.soloader.DoNotOptimize

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.common.internal.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.common.internal.DoNotStrip *;
}

# Do not strip any method/class that is annotated with @DoNotOptimize
-keep @com.facebook.soloader.DoNotOptimize class *
-keepclassmembers class * {
    @com.facebook.soloader.DoNotOptimize *;
}

# Keep native methods
-keepclassmembers class com.facebook.** {
    native <methods>;
}

# Do not strip SoLoader class and init method
-keep public class com.facebook.soloader.SoLoader {
    public static void init(android.content.Context, int);
}

-dontwarn okio.**
-dontwarn com.squareup.okhttp.**
-dontwarn okhttp3.**
-dontwarn javax.annotation.**
-dontwarn com.android.volley.toolbox.**
-dontwarn com.facebook.infer.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\7993801be14c17972a1b6e6d66193579\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\7993801be14c17972a1b6e6d66193579\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\54a35eb80f2aa5f7c882dfa263e26c0f\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\54a35eb80f2aa5f7c882dfa263e26c0f\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\6aa413ed70189dce5e6dec98c0593a3d\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\6aa413ed70189dce5e6dec98c0593a3d\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4293c340b99443ca65d8cdb33d88d4f5\transformed\rules\lib\META-INF\proguard\okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4293c340b99443ca65d8cdb33d88d4f5\transformed\rules\lib\META-INF\proguard\okio.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\707d01463a44dd26633178c599a03113\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\kotlin-reflect.pro
# When editing this file, update the following files as well:
# - META-INF/com.android.tools/proguard/kotlin-reflect.pro
# - META-INF/com.android.tools/r8-upto-1.6.0/kotlin-reflect.pro
# - META-INF/proguard/kotlin-reflect.pro
# Keep Metadata annotations so they can be parsed at runtime.
-keep class kotlin.Metadata { *; }

# Keep generic signatures and annotations at runtime.
# R8 requires InnerClasses and EnclosingMethod if you keepattributes Signature.
-keepattributes InnerClasses,Signature,RuntimeVisible*Annotations,EnclosingMethod

# Don't note on API calls from different JVM versions as they're gated properly at runtime.
-dontnote kotlin.internal.PlatformImplementationsKt

# Don't note on internal APIs, as there is some class relocating that shrinkers may unnecessarily find suspicious.
-dontwarn kotlin.reflect.jvm.internal.**

# Statically guarded by try-catch block and not used on Android, see CacheByClass
-dontwarn java.lang.ClassValue

# Do not even execute try-catch block for ClassValue
-assumenosideeffects class kotlin.reflect.jvm.internal.CacheByClassKt {
    boolean useClassValue return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\707d01463a44dd26633178c599a03113\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\kotlin-reflect.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3799ca4cacc8bd2e526237b08c58c707\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3799ca4cacc8bd2e526237b08c58c707\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\proguard.txt
#https://github.com/leolin310148/ShortcutBadger/issues/46
-keep class me.leolin.shortcutbadger.impl.AdwHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.ApexHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.AsusHomeLauncher { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.DefaultBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.NewHtcHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.NovaHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.SolidHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.SonyHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.XiaomiHomeBadger { <init>(...); }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>