import { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Platform,
  AppState,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { collection, query, orderBy, limit, getDocs, where, Timestamp, updateDoc, doc, arrayUnion, arrayRemove, increment, setDoc, deleteDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { getBroadCategories } from '../utils/categoryHierarchy';

const SCREEN_WIDTH = Dimensions.get('window').width;
const NUM_COLUMNS = 2;

const getRankBadgeStyle = (rank) => {
  if (rank === 1) return { backgroundColor: '#FFD700', borderColor: '#FFD700' }; // Gold
  if (rank === 2) return { backgroundColor: '#C0C0C0', borderColor: '#C0C0C0' }; // Silver
  if (rank === 3) return { backgroundColor: '#CD7F32', borderColor: '#CD7F32' }; // Bronze
  return { backgroundColor: 'rgba(0,0,0,0.6)', borderColor: '#fff' };
};

const LeaderboardScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [items, setItems] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All'); const [selectedGender, setSelectedGender] = useState('All');
  const [showGenderDropdown, setShowGenderDropdown] = useState(false);
  const [limitCount, setLimitCount] = useState(50);
  const [dataLoaded, setDataLoaded] = useState(false);
  const userId = auth.currentUser?.uid;
  const appStateRef = useRef(AppState.currentState);
  const authStateRef = useRef(userId);

  const categories = ['All', ...getBroadCategories()];
  const genders = ['All', 'Male', 'Female', 'Unisex'];
  const fetchLeaderboardItems = useCallback(async (forceRefresh = false) => {
    // If data is already loaded and this is not a forced refresh, don't fetch again
    if (dataLoaded && !forceRefresh && items.length > 0) {
      console.log('Top Picks: Data already loaded, skipping fetch');
      return;
    }

    console.log('Top Picks: Fetching leaderboard items...');
    setLoading(true);
    try {
      let leaderboardQuery = collection(db, 'clothingItems');

      // Build Firestore query with likeCount as primary sort field
      let orderFields = [
        orderBy('likeCount', 'desc'),
        orderBy('category', 'asc'),
        orderBy('createdAt', 'desc'),
        orderBy('__name__', 'desc'),
        limit(limitCount)
      ]; if (selectedCategory !== 'All') {
        // For specific categories, we'll fetch all items and filter client-side
        // to handle different category field names (broadCategory, detailedCategory, category)
        leaderboardQuery = collection(db, 'clothingItems');
      }

      // Apply all orderBy and limit at the end
      leaderboardQuery = query(leaderboardQuery, ...orderFields);

      const snapshot = await getDocs(leaderboardQuery);

      if (!snapshot.empty) {        // First, extract the data from the snapshot
        let itemsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          likedBy: doc.data().likedBy || [],
        }));

        // Filter by selected category if not 'All'
        if (selectedCategory !== 'All') {
          itemsData = itemsData.filter(item => {
            // Check multiple category fields for compatibility
            return item.broadCategory === selectedCategory ||
              item.detailedCategory === selectedCategory ||
              item.category === selectedCategory;
          });
        }

        // Filter by selected gender if not 'All'
        if (selectedGender !== 'All') {
          itemsData = itemsData.filter(item => {
            return item.gender && item.gender.toLowerCase() === selectedGender.toLowerCase();
          });
        }

        // Sort all items by likeCount first (descending)
        itemsData.sort((a, b) => {
          // Ensure likeCount is a number (default to 0 if undefined)
          const aLikes = typeof a.likeCount === 'number' ? a.likeCount : 0;
          const bLikes = typeof b.likeCount === 'number' ? b.likeCount : 0;

          // First sort by likeCount (descending)
          if (bLikes !== aLikes) {
            return bLikes - aLikes;
          }

          // If likeCount is the same, sort by category (ascending)
          const aCategory = a.category || 'Uncategorized';
          const bCategory = b.category || 'Uncategorized';
          if (aCategory !== bCategory) {
            return aCategory.localeCompare(bCategory);
          }

          // If category is the same, sort by createdAt (descending)
          if (a.createdAt && b.createdAt) {
            // Handle Firestore timestamps
            if (a.createdAt.seconds && b.createdAt.seconds) {
              return b.createdAt.seconds - a.createdAt.seconds;
            }
            // Handle Date objects
            if (a.createdAt instanceof Date && b.createdAt instanceof Date) {
              return b.createdAt.getTime() - a.createdAt.getTime();
            }
          }

          // Fallback to ID comparison if all else is equal
          return a.id.localeCompare(b.id);
        });

        // Assign ranks to the sorted items
        const rankedItems = itemsData.map((item, index) => ({
          ...item,
          rank: index + 1
        }));

        setItems(rankedItems);
        console.log('Top Picks: Items sorted by like count and ranked');
      } else {
        setItems([]);
      }

      // Mark data as loaded
      setDataLoaded(true);
    } catch (error) {
      console.error('Error fetching leaderboard items:', error);
      if (error.code === 'failed-precondition') {
        alert('Database index required. Please check Firestore console (likely for createdAt or likeCount).');
      } else {
        alert('Error loading leaderboard.');
      }
      setItems([]);
    } finally {
      setLoading(false);
    }
  }, [selectedCategory, selectedGender, limitCount, dataLoaded, items.length]);

  // Set up app state listener to detect when app comes back from background
  useEffect(() => {
    // Initial fetch when component mounts
    fetchLeaderboardItems();

    // Set up app state change listener to refresh data when app is reopened
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        console.log('Top Picks: App has come to the foreground, refreshing data');
        fetchLeaderboardItems(true);
      }
      appStateRef.current = nextAppState;
    });

    // Set up auth state listener to refresh data when user logs in/out
    const unsubscribeAuth = auth.onAuthStateChanged(user => {
      const newUserId = user?.uid;
      if (newUserId !== authStateRef.current) {
        console.log('Top Picks: Auth state changed, refreshing data');
        authStateRef.current = newUserId;
        fetchLeaderboardItems(true);
      }
    });

    return () => {
      subscription.remove();
      unsubscribeAuth();
    };
  }, [fetchLeaderboardItems]);
  // Only fetch data when category, gender, or limit changes
  useEffect(() => {
    fetchLeaderboardItems(true);
  }, [selectedCategory, selectedGender, limitCount]);

  // We're intentionally NOT using useFocusEffect to prevent re-fetching on tab switches
  const toggleLike = async (itemId, currentlyLiked) => {
    if (!userId) {
      alert("You must be logged in to like items.");
      return;
    }

    // Update UI immediately for better user experience and re-sort items
    setItems(currentItems => {
      // First, update the like count for the specific item
      const updatedItems = currentItems.map(item => {
        if (item.id === itemId) {
          const updatedLikedBy = currentlyLiked
            ? item.likedBy.filter(id => id !== userId)
            : [...item.likedBy, userId];
          let newLikeCount = currentlyLiked ? (item.likeCount || 1) - 1 : (item.likeCount || 0) + 1;
          if (newLikeCount < 0) newLikeCount = 0;
          return {
            ...item,
            likedBy: updatedLikedBy,
            likeCount: newLikeCount,
          };
        }
        return item;
      });

      // Then, re-sort the items based on the updated like counts
      // Sort all items by likeCount first (descending)
      updatedItems.sort((a, b) => {
        // Ensure likeCount is a number (default to 0 if undefined)
        const aLikes = typeof a.likeCount === 'number' ? a.likeCount : 0;
        const bLikes = typeof b.likeCount === 'number' ? b.likeCount : 0;

        // First sort by likeCount (descending)
        if (bLikes !== aLikes) {
          return bLikes - aLikes;
        }

        // If likeCount is the same, sort by category (ascending)
        const aCategory = a.category || 'Uncategorized';
        const bCategory = b.category || 'Uncategorized';
        if (aCategory !== bCategory) {
          return aCategory.localeCompare(bCategory);
        }

        // If category is the same, sort by createdAt (descending)
        if (a.createdAt && b.createdAt) {
          // Handle Firestore timestamps
          if (a.createdAt.seconds && b.createdAt.seconds) {
            return b.createdAt.seconds - a.createdAt.seconds;
          }
          // Handle Date objects
          if (a.createdAt instanceof Date && b.createdAt instanceof Date) {
            return b.createdAt.getTime() - a.createdAt.getTime();
          }
        }

        // Fallback to ID comparison if all else is equal
        return a.id.localeCompare(b.id);
      });

      // Use the sorted items directly
      const sortedItems = updatedItems;

      // Update the rank property for each item
      return sortedItems.map((item, index) => ({
        ...item,
        rank: index + 1
      }));
    });

    try {
      // Get references to the required documents
      const itemRef = doc(db, 'clothingItems', itemId);
      const userLikesRef = doc(db, 'users', userId, 'likes', itemId);

      if (currentlyLiked) {
        // User is unliking the item
        await updateDoc(itemRef, {
          likedBy: arrayRemove(userId),
          likeCount: increment(-1)
        });

        // Remove from user's likes collection
        try {
          await deleteDoc(userLikesRef);
        } catch (error) {
          console.error("Error removing from likes collection:", error);
        }

        // After update, ensure likeCount is not negative in Firestore
        const itemSnap = await getDocs(query(collection(db, 'clothingItems'), where('__name__', '==', itemId)));
        const docData = itemSnap.docs[0]?.data();
        if (docData && docData.likeCount < 0) {
          await updateDoc(itemRef, { likeCount: 0 });
        }
        console.log(`User ${userId} unliked item ${itemId}`);
      } else {
        // User is liking the item
        await updateDoc(itemRef, {
          likedBy: arrayUnion(userId),
          dislikedBy: arrayRemove(userId),
          likeCount: increment(1)
        });

        // Add to user's likes collection
        try {
          await setDoc(userLikesRef, {
            itemId: itemId,
            likedAt: Timestamp.now()
          });
        } catch (error) {
          console.error("Error adding to likes collection:", error);
        }

        console.log(`User ${userId} liked item ${itemId}`);
      }
    } catch (error) {
      console.error("Error updating like status:", error);
      alert("Failed to update like status. Please try again.");

      // If there was an error, revert the UI changes and re-sort
      setItems(currentItems => {
        // First, revert the like count for the specific item
        const revertedItems = currentItems.map(item => {
          if (item.id === itemId) {
            const originalLikedBy = currentlyLiked
              ? [...item.likedBy, userId]
              : item.likedBy.filter(id => id !== userId);
            const originalLikeCount = currentlyLiked
              ? (item.likeCount || 0) + 1
              : (item.likeCount || 1) - 1;
            return {
              ...item,
              likedBy: originalLikedBy,
              likeCount: originalLikeCount < 0 ? 0 : originalLikeCount,
            };
          }
          return item;
        });

        // Re-sort with the reverted values using the same sorting logic
        revertedItems.sort((a, b) => {
          // Ensure likeCount is a number (default to 0 if undefined)
          const aLikes = typeof a.likeCount === 'number' ? a.likeCount : 0;
          const bLikes = typeof b.likeCount === 'number' ? b.likeCount : 0;

          // First sort by likeCount (descending)
          if (bLikes !== aLikes) {
            return bLikes - aLikes;
          }

          // If likeCount is the same, sort by category (ascending)
          const aCategory = a.category || 'Uncategorized';
          const bCategory = b.category || 'Uncategorized';
          if (aCategory !== bCategory) {
            return aCategory.localeCompare(bCategory);
          }

          // If category is the same, sort by createdAt (descending)
          if (a.createdAt && b.createdAt) {
            // Handle Firestore timestamps
            if (a.createdAt.seconds && b.createdAt.seconds) {
              return b.createdAt.seconds - a.createdAt.seconds;
            }
            // Handle Date objects
            if (a.createdAt instanceof Date && b.createdAt instanceof Date) {
              return b.createdAt.getTime() - a.createdAt.getTime();
            }
          }

          // Fallback to ID comparison if all else is equal
          return a.id.localeCompare(b.id);
        });

        // Use the sorted items directly
        const sortedItems = revertedItems;

        return sortedItems.map((item, index) => ({
          ...item,
          rank: index + 1
        }));
      });
    }
  };

  const renderItem = ({ item, index }) => {
    const isLiked = userId ? item.likedBy?.includes(userId) : false;
    // Use the item's rank property instead of the index for the badge
    const rank = item.rank || index + 1;
    const badgeStyle = getRankBadgeStyle(rank);

    return (
      <View style={styles.gridItemOuterContainer}>
        <TouchableOpacity
          style={styles.gridItemInnerContainer}
          onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
          activeOpacity={0.85}
        >
          <Image
            source={{ uri: item.imageUrl || 'https://via.placeholder.com/200?text=N/A' }}
            style={styles.itemImage}
          />
          <View style={[styles.rankOverlay, badgeStyle]}>
            <Text style={styles.rankText}>{rank}</Text>
          </View>          <View style={styles.itemInfo}>
            <Text style={styles.itemTitle} numberOfLines={1}>
              {item.title || item.category || 'Untitled'}
            </Text>
            {item.gender && (
              <Text style={styles.itemGender} numberOfLines={1}>
                {item.gender}
              </Text>
            )}
            <View style={styles.likesContainer}>
              <Ionicons name="heart" size={16} color="#FF6B6B" style={{ marginRight: 2 }} />
              <Text style={styles.likeCount}>{item.likeCount || 0}</Text>
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.likeButton}
          onPress={() => toggleLike(item.id, isLiked)}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isLiked ? "heart" : "heart-outline"}
            size={28}
            color={isLiked ? "#FF6B6B" : "#bbb"}
          />
        </TouchableOpacity>
      </View>
    );
  }; const renderFilterRow = (data, selectedValue, onSelect, style) => (
    <View style={[styles.filtersContainer, style]}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={data}
        keyExtractor={(item) => item}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedValue === item && styles.activeFilterButton,
            ]}
            onPress={() => onSelect(item)}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedValue === item && styles.activeFilterText,
              ]}
            >
              {item}
            </Text>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.categoriesContentContainer}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Top Picks</Text>
          <Text style={styles.headerSubtitle}>Discover trending styles</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.genderDropdown}
            onPress={() => setShowGenderDropdown(true)}
          >
            <Text style={styles.genderDropdownText}>{selectedGender}</Text>
            <Ionicons name="chevron-down" size={16} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => fetchLeaderboardItems(true)} style={styles.headerButton}>
            <Ionicons name="refresh" size={24} color="#FF6B6B" />
          </TouchableOpacity>
        </View>
      </View>      {/* Category Filter Only */}
      {renderFilterRow(categories, selectedCategory, setSelectedCategory, styles.categoryFilters)}

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
        </View>
      ) : items.length > 0 ? (
        <>
          <FlatList
            data={items}
            keyExtractor={(item) => item.id}
            renderItem={renderItem}
            numColumns={NUM_COLUMNS}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            extraData={items}
          />
          {items.length >= limitCount && (
            <TouchableOpacity style={{ alignSelf: 'center', margin: 16, padding: 12, backgroundColor: '#FF6B6B', borderRadius: 20 }} onPress={() => setLimitCount(lc => lc + 50)}>
              <Text style={{ color: '#fff', fontWeight: 'bold' }}>Load More</Text>
            </TouchableOpacity>
          )}
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="sad-outline" size={60} color="#ccc" />
          <Text style={styles.emptyText}>No top picks found</Text>
          <Text style={styles.emptySubText}>Try adjusting the filters.</Text>        </View>
      )}

      {/* Gender Dropdown Modal */}
      <Modal
        visible={showGenderDropdown}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowGenderDropdown(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowGenderDropdown(false)}
        >
          <View style={styles.dropdownModal}>
            <Text style={styles.dropdownTitle}>Select Gender</Text>
            {genders.map((gender) => (
              <TouchableOpacity
                key={gender}
                style={[
                  styles.dropdownItem,
                  selectedGender === gender && styles.selectedDropdownItem
                ]}
                onPress={() => {
                  setSelectedGender(gender);
                  setShowGenderDropdown(false);
                }}
              >
                <Text style={[
                  styles.dropdownItemText,
                  selectedGender === gender && styles.selectedDropdownItemText
                ]}>
                  {gender}
                </Text>
                {selectedGender === gender && (
                  <Ionicons name="checkmark" size={20} color="#FF6B6B" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f6f7fb', // subtle background
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 55 : 45,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#FF6B6B',
    letterSpacing: 0.5,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888',
    letterSpacing: 0.3,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    padding: 10,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
    backgroundColor: '#f8f8f8',
  },
  genderDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginHorizontal: 8,
  },
  genderDropdownText: {
    fontSize: 14,
    color: '#666',
    marginRight: 4,
    fontWeight: '500',
  }, filtersContainer: {
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryFilters: {}, categoriesContentContainer: {
    paddingHorizontal: 5,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginHorizontal: 4,
    marginVertical: 2,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  activeFilterButton: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 10,
    paddingBottom: 30,
  },
  gridItemOuterContainer: {
    flex: 1 / NUM_COLUMNS,
    margin: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.10,
    shadowRadius: 6,
    elevation: 4,
    position: 'relative',
    minHeight: 260,
  },
  gridItemInnerContainer: {
    overflow: 'hidden',
    borderRadius: 16,
    flex: 1,
  },
  itemImage: {
    width: '100%',
    height: SCREEN_WIDTH / NUM_COLUMNS * 1.1,
    backgroundColor: '#eee',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  rankOverlay: {
    position: 'absolute',
    top: 12,
    left: 12,
    borderRadius: 16,
    borderWidth: 2,
    paddingHorizontal: 12,
    paddingVertical: 4,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 36,
  },
  rankText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0,0,0,0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  itemInfo: {
    padding: 12,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    minHeight: 60,
    justifyContent: 'center',
  }, itemTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 6,
  },
  itemGender: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  likesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  likeCount: {
    fontSize: 14,
    color: '#FF6B6B',
    marginLeft: 2,
    fontWeight: 'bold',
  },
  likeButton: {
    position: 'absolute',
    bottom: 18,
    right: 18,
    backgroundColor: '#fff',
    borderRadius: 22,
    padding: 8,
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.10,
    shadowRadius: 4,
    elevation: 2,
    zIndex: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 15,
    textAlign: 'center',
  }, emptySubText: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    maxWidth: 250,
    width: '80%',
    maxHeight: 300,
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginBottom: 5,
  },
  selectedDropdownItem: {
    backgroundColor: '#FF6B6B20',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  selectedDropdownItemText: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
});

export default LeaderboardScreen;