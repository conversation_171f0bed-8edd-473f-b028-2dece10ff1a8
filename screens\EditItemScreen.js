import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  Modal,
  StatusBar
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { doc, getDoc, updateDoc, serverTimestamp, collection, addDoc, getDocs, deleteDoc } from 'firebase/firestore';
import { db, auth, storage } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import HybridCategorySelector from '../components/HybridCategorySelector';
import ColorSelector from '../components/ColorSelector';
import { validateCategorySelection, getBroadCategories } from '../utils/categoryHierarchy';

// Default categories - using the complete category hierarchy
const defaultCategories = getBroadCategories();

const EditItemScreen = ({ route, navigation }) => {
  const { itemId } = route.params;
  const [images, setImages] = useState([]); // Array of image URIs
  const [selectedImageIndex, setSelectedImageIndex] = useState(0); // Track which image is displayed in the preview
  const [originalImageUrls, setOriginalImageUrls] = useState([]); // Store original image URLs

  // Category state - single broad category, multiple detailed categories
  const [selectedBroadCategory, setSelectedBroadCategory] = useState('');
  const [selectedDetailedCategories, setSelectedDetailedCategories] = useState([]);

  // Color selection state (multiple colors)
  const [selectedColors, setSelectedColors] = useState([]);

  // Gender selection state
  const [selectedGender, setSelectedGender] = useState('');

  const [tags, setTags] = useState(''); // Additional tags for better matching
  const [brand, setBrand] = useState('');
  const [size, setSize] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [buyLink, setBuyLink] = useState('');
  const [price, setPrice] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [loading, setLoading] = useState(true);
  const [isSeller, setIsSeller] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const user = auth.currentUser;

  useEffect(() => {
    // Check if user is a seller and fetch item details
    const checkSellerStatusAndFetchItem = async () => {
      if (user) {
        try {
          // Check if user is a seller
          const userDocRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setIsSeller(userData.isSeller || false);
          } else {
            setIsSeller(false);
          }

          // Fetch item details
          const itemDocRef = doc(db, 'clothingItems', itemId);
          const itemDoc = await getDoc(itemDocRef);

          if (itemDoc.exists()) {
            const itemData = itemDoc.data();
            console.log('EditItemScreen - Loading item data:', JSON.stringify(itemData, null, 2));

            // Check if current user is the owner of this item
            setIsOwner(itemData.userId === user.uid);

            // Set item data to state
            setTitle(itemData.title || '');
            setDescription(itemData.description || '');

            // Handle category data - check multiple possible field names
            let broadCat = '';
            let detailedCats = [];

            // Priority order: selectedBroadCategory > broadCategory > category
            if (itemData.selectedBroadCategory) {
              broadCat = itemData.selectedBroadCategory;
              detailedCats = itemData.selectedDetailedCategories || [];
              console.log('EditItemScreen - Using selectedBroadCategory format');
            } else if (itemData.broadCategory) {
              broadCat = itemData.broadCategory;
              detailedCats = itemData.detailedCategories || [];
              console.log('EditItemScreen - Using broadCategory format');
            } else if (itemData.category) {
              // Legacy single category format
              console.log('EditItemScreen - Using legacy category format');

              try {
                const { getBroadCategories, isBroadCategory } = require('../utils/categoryHierarchy');

                if (isBroadCategory(itemData.category)) {
                  broadCat = itemData.category;
                  detailedCats = [];
                } else {
                  const { getBroadCategoryForDetailed } = require('../utils/categoryHierarchy');
                  const mappedBroadCategory = getBroadCategoryForDetailed(itemData.category);

                  if (mappedBroadCategory) {
                    broadCat = mappedBroadCategory;
                    detailedCats = [itemData.category];
                  } else {
                    broadCat = itemData.category;
                    detailedCats = [];
                  }
                }
              } catch (error) {
                console.warn('EditItemScreen - Error mapping legacy category:', error);
                broadCat = itemData.category;
                detailedCats = [];
              }
            }

            console.log('EditItemScreen - Final category mapping:', {
              broad: broadCat,
              detailed: detailedCats,
              originalData: {
                selectedBroadCategory: itemData.selectedBroadCategory,
                broadCategory: itemData.broadCategory,
                category: itemData.category,
                selectedDetailedCategories: itemData.selectedDetailedCategories,
                detailedCategories: itemData.detailedCategories
              }
            });

            setSelectedBroadCategory(broadCat);
            setSelectedDetailedCategories(detailedCats);

            // Handle colors - check multiple possible field names
            let colors = [];
            if (itemData.selectedColors) {
              colors = itemData.selectedColors;
              console.log('EditItemScreen - Using selectedColors field');
            } else if (itemData.colors) {
              colors = itemData.colors;
              console.log('EditItemScreen - Using colors field');
            }

            console.log('EditItemScreen - Final colors mapping:', {
              colors: colors,
              originalData: {
                selectedColors: itemData.selectedColors,
                colors: itemData.colors
              }
            });

            setSelectedColors(colors);

            // Handle gender selection
            const gender = itemData.gender || '';
            console.log('EditItemScreen - Loading gender:', gender);
            setSelectedGender(gender);

            // Handle tags - convert array to string if needed
            let tagsString = '';
            if (Array.isArray(itemData.tags)) {
              tagsString = itemData.tags.join(', ');
              console.log('EditItemScreen - Converting tags array to string:', itemData.tags, '->', tagsString);
            } else if (typeof itemData.tags === 'string') {
              tagsString = itemData.tags;
              console.log('EditItemScreen - Using tags string:', tagsString);
            }
            setTags(tagsString);

            setBrand(itemData.brand || '');
            setSize(itemData.size || '');
            setBuyLink(itemData.buyLink || '');
            setPrice(itemData.price ? itemData.price.toString() : '');

            // Handle both single and multiple images
            if (itemData.imageUrls && Array.isArray(itemData.imageUrls)) {
              // New format with multiple images
              setImages(itemData.imageUrls);
              setOriginalImageUrls(itemData.imageUrls);
              if (itemData.imageUrls.length > 0) {
                setSelectedImageIndex(0);
              }
            } else if (itemData.imageUrl) {
              // Old format with single image
              setImages([itemData.imageUrl]);
              setOriginalImageUrls([itemData.imageUrl]);
              setSelectedImageIndex(0);
            }
          } else {
            Alert.alert('Error', 'Item not found');
            navigation.goBack();
          }
        } catch (error) {
          console.error('Error checking seller status or fetching item:', error);
          Alert.alert('Error', 'Failed to load item details');
          navigation.goBack();
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
        setIsSeller(false);
        Alert.alert('Error', 'You must be logged in to edit items');
        navigation.goBack();
      }
    };

    // Request camera roll permissions
    const requestPermissions = async () => {
      if (Platform.OS !== 'web') {
        try {
          console.log('Requesting initial media library permissions...');
          const libraryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
          console.log('Initial media library permission status:', libraryStatus.status);

          if (libraryStatus.status !== 'granted') {
            console.log('Media library permission not granted');
            Alert.alert(
              'Permission Required',
              'This app needs access to your photo library to upload clothing images. Please grant permission when prompted or enable it in your device settings.'
            );
          } else {
            console.log('Media library permission granted');
          }
        } catch (error) {
          console.error('Error requesting permissions:', error);
        }
      }
    };

    checkSellerStatusAndFetchItem();
    requestPermissions();
  }, [user, itemId, navigation]);

  const showImagePickerOptions = () => {
    setShowImagePickerModal(true);
  };

  const pickImageFromLibrary = async () => {
    setShowImagePickerModal(false);
    try {
      console.log('pickImageFromLibrary function called');

      // Check permissions first
      if (Platform.OS !== 'web') {
        console.log('Requesting media library permissions...');
        const libraryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
        console.log('Media library permission status:', libraryStatus.status);

        if (libraryStatus.status !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Sorry, we need camera roll permissions to upload images! Please enable permissions in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => console.log('User should open settings') }
            ]
          );
          return;
        }
      }

      console.log('Launching image library...');
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsMultipleSelection: true, // Allow multiple image selection
        selectionLimit: 5, // Limit to 5 images
        quality: 0.7,
      });

      console.log('ImagePicker result:', JSON.stringify(result, null, 2));
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error in pickImageFromLibrary:', error);
      Alert.alert(
        'Image Picker Error',
        `Failed to open image library: ${error.message}. Please try again or check app permissions.`
      );
    }
  };

  const pickImageFromCamera = async () => {
    setShowImagePickerModal(false);
    try {
      console.log('pickImageFromCamera function called');

      // Check camera permissions first
      if (Platform.OS !== 'web') {
        console.log('Requesting camera permissions...');
        const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
        console.log('Camera permission status:', cameraStatus.status);

        if (cameraStatus.status !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Sorry, we need camera permissions to take photos! Please enable permissions in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => console.log('User should open settings') }
            ]
          );
          return;
        }
      }

      console.log('Launching camera...');
      let result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        quality: 0.7,
      });

      console.log('Camera result:', JSON.stringify(result, null, 2));
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error in pickImageFromCamera:', error);
      Alert.alert(
        'Camera Error',
        `Failed to open camera: ${error.message}. Please try again or check app permissions.`
      );
    }
  };

  const handleImagePickerResult = (result) => {
    if (!result.canceled && result.assets && result.assets.length > 0) {
      console.log('Images selected:', result.assets.length);
      // Add new images to the existing array
      const newImages = result.assets.map(asset => asset.uri);

      setImages(prevImages => {
        // Combine previous and new images, limit to 5 total
        const combinedImages = [...prevImages, ...newImages];
        const finalImages = combinedImages.slice(0, 5); // Limit to 5 images

        console.log('Final images count:', finalImages.length);

        // If this is the first image being added, set it as selected
        if (prevImages.length === 0 && finalImages.length > 0) {
          setSelectedImageIndex(0);
        }

        return finalImages;
      });
    } else {
      console.log('No images selected or operation was canceled');
      if (result.canceled) {
        console.log('User canceled image selection');
      } else {
        console.log('No assets found in result');
      }
    }
  };

  // Keep the old function name for backward compatibility
  const pickImage = showImagePickerOptions;

  const removeImage = (indexToRemove) => {
    setImages(prevImages => {
      const newImages = prevImages.filter((_, index) => index !== indexToRemove);

      // Adjust selected index if necessary
      if (indexToRemove === selectedImageIndex) {
        if (newImages.length > 0) {
          setSelectedImageIndex(Math.min(selectedImageIndex, newImages.length - 1));
        } else {
          setSelectedImageIndex(0);
        }
      } else if (indexToRemove < selectedImageIndex) {
        setSelectedImageIndex(selectedImageIndex - 1);
      }

      return newImages;
    });
  };

  const uploadImagesAndGetURLs = async (imageUris) => {
    if (!imageUris || imageUris.length === 0) return [];

    setUploading(true);
    setUploadProgress(0);

    const uploadPromises = imageUris.map(async (uri, index) => {
      // If image hasn't changed (is from original), return the original URL
      if (originalImageUrls.includes(uri)) {
        return uri;
      }

      // Compress and resize image before upload
      let compressedUri = uri;
      try {
        const manipResult = await ImageManipulator.manipulateAsync(
          uri,
          [{ resize: { width: 1080 } }], // Resize to max width 1080px
          { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
        );
        compressedUri = manipResult.uri;
      } catch (err) {
        console.warn('Image compression failed, uploading original:', err);
      }

      const response = await fetch(compressedUri);
      const blob = await response.blob();
      const fileExtension = compressedUri.split('.').pop();
      const fileName = `${Date.now()}_${index}.${fileExtension}`;
      const storageRef = ref(storage, `clothingItems/${user.uid}/${fileName}`);

      return new Promise((resolve, reject) => {
        const uploadTask = uploadBytesResumable(storageRef, blob);

        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            // Update progress for this specific image
            setUploadProgress(prev => Math.max(prev, progress / imageUris.length));
          },
          (error) => {
            console.error('Upload failed:', error);
            reject(error);
          },
          () => {
            getDownloadURL(uploadTask.snapshot.ref)
              .then((downloadURL) => {
                resolve(downloadURL);
              })
              .catch((error) => {
                console.error('Failed to get download URL:', error);
                reject(error);
              });
          }
        );
      });
    });

    try {
      const uploadedUrls = await Promise.all(uploadPromises);
      setUploading(false);
      setUploadProgress(0);
      return uploadedUrls;
    } catch (error) {
      console.error('Failed to upload images:', error);
      Alert.alert('Upload Failed', 'Could not upload one or more images. Please try again.');
      setUploading(false);
      setUploadProgress(0);
      throw error;
    }
  };

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert('Not Logged In', 'You must be logged in to edit items.');
      return;
    }

    if (!isOwner) {
      Alert.alert('Permission Denied', 'You can only edit your own items.');
      return;
    }

    if (images.length === 0) {
      Alert.alert('Missing Images', 'Please select at least one image for the item.');
      return;
    }

    if (!selectedBroadCategory) {
      Alert.alert('Missing Category', 'Please select a category type for the item.');
      return;
    }

    if (!selectedGender) {
      Alert.alert('Missing Gender', 'Please select a gender category (Male, Female, or Unisex).');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Missing Title', 'Please enter a title for the item.');
      return;
    }

    if (!price.trim()) {
      Alert.alert('Missing Price', 'Please provide a price for your item.');
      return;
    }

    // Validate that price is a valid number
    const priceValue = parseFloat(price.trim());
    if (isNaN(priceValue) || priceValue <= 0) {
      Alert.alert('Invalid Price', 'Please provide a valid price for your item.');
      return;
    }

    // Validate category selection
    const categoryValidation = validateCategorySelection(selectedBroadCategory, selectedDetailedCategories);
    if (!categoryValidation.isValid) {
      Alert.alert('Category Error', categoryValidation.message);
      return;
    }

    setLoading(true);
    try {
      const imageUrls = await uploadImagesAndGetURLs(images);
      if (imageUrls.length === 0) {
        setLoading(false);
        return;
      }

      const itemDocRef = doc(db, 'clothingItems', itemId);
      const updatedItem = {
        // Image data
        imageUrls: imageUrls,
        imageUrl: imageUrls[0], // Keep backward compatibility

        // Category data - save in both old and new formats for compatibility
        selectedBroadCategory: selectedBroadCategory,
        selectedDetailedCategories: selectedDetailedCategories,
        broadCategory: selectedBroadCategory, // Also save in old format
        detailedCategories: selectedDetailedCategories, // Also save in old format
        category: selectedBroadCategory, // Legacy single category field        // Color data - save in both formats
        selectedColors: selectedColors,
        colors: selectedColors, // Also save in old format

        // Gender data
        gender: selectedGender,

        // Other fields
        tags: tags.trim() ? tags.trim().split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [],
        title: title.trim(),
        description: description.trim(),
        brand: brand.trim(),
        size: size.trim(),
        buyLink: buyLink.trim(),
        price: parseFloat(price.trim()), // Store price as a number
        updatedAt: serverTimestamp(),
      };

      console.log('EditItemScreen - Saving updated item:', JSON.stringify(updatedItem, null, 2));

      await updateDoc(itemDocRef, updatedItem);

      Alert.alert('Success', 'Item updated successfully!');
      navigation.goBack();
    } catch (error) {
      console.error('Error updating item:', error);
      Alert.alert('Update Error', 'Could not update item details.');
    } finally {
      setLoading(false);
      setUploading(false);
    }
  };

  // If user is not a seller or not the owner, show a message
  if (!loading && (!isSeller || !isOwner)) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Item</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.notSellerContainer}>
          <Ionicons name="alert-circle-outline" size={60} color="#FF6B6B" />
          <Text style={styles.notSellerTitle}>Permission Denied</Text>
          <Text style={styles.notSellerText}>
            {!isSeller
              ? 'Only sellers can edit items. Please update your profile to become a seller.'
              : 'You can only edit your own items.'}
          </Text>
          <TouchableOpacity
            style={styles.becomeSellerButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.becomeSellerButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Show loading indicator while checking seller status
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={{ marginTop: 10, color: '#555' }}>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      <ScrollView
        style={styles.container}
        contentContainerStyle={{ paddingBottom: 20, paddingTop: Platform.OS === 'ios' ? 50 : 0 }}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            disabled={uploading}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Item</Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Image Section */}
        <View style={styles.imageSection}>
          {images.length > 0 ? (
            <View style={styles.imageContainer}>
              {/* Main Image Display */}
              <View style={styles.mainImageContainer}>
                <Image
                  source={{ uri: images[selectedImageIndex] }}
                  style={styles.mainImage}
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => removeImage(selectedImageIndex)}
                  disabled={uploading || loading}
                >
                  <Ionicons name="close-circle" size={24} color="#FF6B6B" />
                </TouchableOpacity>
              </View>

              {/* Image Thumbnails */}
              <ScrollView
                horizontal
                style={styles.thumbnailContainer}
                showsHorizontalScrollIndicator={false}
              >
                {images.map((imageUri, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.thumbnail,
                      selectedImageIndex === index && styles.selectedThumbnail
                    ]}
                    onPress={() => setSelectedImageIndex(index)}
                    disabled={uploading || loading}
                  >
                    <Image source={{ uri: imageUri }} style={styles.thumbnailImage} />
                  </TouchableOpacity>
                ))}

                {images.length < 5 && (
                  <TouchableOpacity
                    style={styles.addMoreImagesButton}
                    onPress={() => {
                      console.log('Add more images button pressed');
                      showImagePickerOptions();
                    }}
                    disabled={uploading || loading}
                  >
                    <Ionicons name="add-circle" size={24} color="#FF6B6B" />
                    <Text style={styles.addMoreImagesText}>Add More</Text>
                  </TouchableOpacity>
                )}
              </ScrollView>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.imagePicker}
              onPress={() => {
                console.log('Image picker button pressed');
                pickImage();
              }}
              disabled={uploading}
            >
              <View style={styles.imagePlaceholder}>
                <Ionicons name="camera" size={40} color="#ccc" />
                <Text style={styles.imagePlaceholderText}>Select Images</Text>
                <Text style={styles.imageSubtext}>Tap to add up to 5 images</Text>
              </View>
            </TouchableOpacity>
          )}

          {uploading && (
            <View style={styles.progressContainer}>
              <Text>Uploading: {uploadProgress.toFixed(0)}%</Text>
              <ActivityIndicator size="small" color="#FF6B6B" />
            </View>
          )}
        </View>

        <Text style={styles.label}>Title *</Text>
        <TextInput
          style={styles.input}
          placeholder="Item Title"
          value={title}
          onChangeText={setTitle}
          editable={!uploading}
        />

        <Text style={styles.label}>Description</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Item Description"
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={4}
          editable={!uploading}
        />



        {/* Category Selector */}
        <HybridCategorySelector
          selectedBroadCategory={selectedBroadCategory}
          selectedDetailedCategories={selectedDetailedCategories}
          onBroadCategorySelect={(category) => {
            console.log('EditItemScreen - Broad category selected:', category);
            setSelectedBroadCategory(category);
          }}
          onDetailedCategoriesChange={(categories) => {
            console.log('EditItemScreen - Detailed categories changed:', categories);
            setSelectedDetailedCategories(categories);
          }}
          disabled={loading || uploading}
          style={styles.categorySelector}        />

        {/* Gender Selector */}
        <View style={styles.genderSection}>
          <Text style={styles.label}>Gender</Text>
          <View style={styles.genderOptions}>
            <TouchableOpacity
              style={[styles.genderOption, selectedGender === 'male' && styles.selectedGenderOption]}
              onPress={() => setSelectedGender('male')}
              disabled={loading || uploading}
            >
              <Text style={[styles.genderOptionText, selectedGender === 'male' && styles.selectedGenderOptionText]}>
                Male
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.genderOption, selectedGender === 'female' && styles.selectedGenderOption]}
              onPress={() => setSelectedGender('female')}
              disabled={loading || uploading}
            >
              <Text style={[styles.genderOptionText, selectedGender === 'female' && styles.selectedGenderOptionText]}>
                Female
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.genderOption, selectedGender === 'unisex' && styles.selectedGenderOption]}
              onPress={() => setSelectedGender('unisex')}
              disabled={loading || uploading}
            >
              <Text style={[styles.genderOptionText, selectedGender === 'unisex' && styles.selectedGenderOptionText]}>
                Unisex
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Color Selector */}
        <ColorSelector
          selectedColors={selectedColors}
          onColorsChange={(colors) => {
            console.log('EditItemScreen - Colors changed:', colors);
            setSelectedColors(colors);
          }}
          disabled={loading || uploading}
          style={styles.colorSelector}
        />

        {/* Tags Input */}
        <Text style={styles.label}>Tags (Optional)</Text>
        <TextInput
          style={styles.input}
          placeholder="e.g., cotton, summer, vintage (separate with commas)"
          value={tags}
          onChangeText={setTags}
          editable={!loading && !uploading}
          maxLength={200}
        />

        <Text style={styles.label}>Brand (Optional)</Text>
        <TextInput
          style={styles.input}
          placeholder="e.g., Nike, Zara"
          value={brand}
          onChangeText={setBrand}
          editable={!uploading}
        />

        <Text style={styles.label}>Size (Optional)</Text>
        <TextInput
          style={styles.input}
          placeholder="e.g., M, UK 10, 42"
          value={size}
          onChangeText={setSize}
          editable={!uploading}
        />
        <Text style={styles.label}>Buy Link (Optional)</Text>
        <TextInput
          style={styles.input}
          placeholder="https://example.com/product-link (optional)"
          value={buyLink}
          onChangeText={setBuyLink}
          keyboardType="url"
          autoCapitalize="none"
          editable={!uploading}
        />

        <Text style={styles.label}>Price</Text>
        <View style={styles.priceInputContainer}>
          <Text style={styles.currencySymbol}>₹</Text>
          <TextInput
            style={styles.priceInput}
            placeholder="Enter price"
            value={price}
            onChangeText={setPrice}
            keyboardType="numeric"
            editable={!uploading}
          />
        </View>

        <TouchableOpacity
          style={[styles.submitButton, (loading || uploading) && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading || uploading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Update Item</Text>
          )}
        </TouchableOpacity>

        {/* Image Picker Modal */}
        <Modal
          visible={showImagePickerModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowImagePickerModal(false)}
        >
          <View style={styles.imagePickerModalOverlay}>
            <View style={styles.imagePickerModalContent}>
              <View style={styles.imagePickerHeader}>
                <Text style={styles.imagePickerTitle}>Add Images</Text>
                <TouchableOpacity
                  onPress={() => setShowImagePickerModal(false)}
                  style={styles.imagePickerCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <Text style={styles.imagePickerSubtitle}>
                Choose how you want to add images to your listing
              </Text>

              <View style={styles.imagePickerOptions}>
                <TouchableOpacity
                  style={styles.imagePickerOption}
                  onPress={pickImageFromLibrary}
                  activeOpacity={0.7}
                >
                  <View style={styles.imagePickerOptionIcon}>
                    <Ionicons name="images" size={32} color="#FF6B6B" />
                  </View>
                  <Text style={styles.imagePickerOptionTitle}>Photo Library</Text>
                  <Text style={styles.imagePickerOptionSubtitle}>
                    Select multiple photos from your gallery
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.imagePickerOption}
                  onPress={pickImageFromCamera}
                  activeOpacity={0.7}
                >
                  <View style={styles.imagePickerOptionIcon}>
                    <Ionicons name="camera" size={32} color="#FF6B6B" />
                  </View>
                  <Text style={styles.imagePickerOptionTitle}>Camera</Text>
                  <Text style={styles.imagePickerOptionSubtitle}>
                    Take a new photo with your camera
                  </Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.imagePickerCancelButton}
                onPress={() => setShowImagePickerModal(false)}
              >
                <Text style={styles.imagePickerCancelText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? 40 : 10,
    paddingBottom: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  notSellerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notSellerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  notSellerText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  becomeSellerButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  becomeSellerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },

  // Image Section Styles
  imageSection: {
    marginVertical: 20,
  },
  imageContainer: {
    marginHorizontal: 20,
  },
  mainImageContainer: {
    position: 'relative',
    marginBottom: 15,
  },
  mainImage: {
    width: '100%',
    height: 300,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 2,
  },
  thumbnailContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: '#FF6B6B',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  addMoreImagesButton: {
    width: 60,
    height: 60,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#FF6B6B',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  addMoreImagesText: {
    color: '#FF6B6B',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  imagePicker: {
    height: 250,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  imagePlaceholder: {
    alignItems: 'center',
  },
  imagePlaceholderText: {
    color: '#aaa',
    marginTop: 10,
    fontSize: 16,
    fontWeight: '600',
  },
  imageSubtext: {
    color: '#ccc',
    marginTop: 5,
    fontSize: 14,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
    marginBottom: 15,
  },
  // Category and Color Section Styles
  categorySelector: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  genderSection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  genderOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    padding: 10,
  },
  genderOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  selectedGenderOption: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  genderOptionText: {
    color: '#555',
    fontWeight: 'bold',
  },
  selectedGenderOptionText: {
    color: '#fff',
  },
  colorSelector: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 20,
    marginBottom: 10,
  },
  input: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#eee',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    marginHorizontal: 20,
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#333',
    marginRight: 5,
  },
  priceInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 40,
  },
  submitButtonDisabled: {
    backgroundColor: '#ffbaba',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Image Picker Modal Styles
  imagePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  imagePickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    paddingHorizontal: 20,
    minHeight: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imagePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  imagePickerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  imagePickerCloseButton: {
    padding: 5,
  },
  imagePickerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  imagePickerOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 30,
  },
  imagePickerOption: {
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    backgroundColor: '#f8f8f8',
    width: '45%',
    borderWidth: 1,
    borderColor: '#eee',
  },
  imagePickerOptionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  imagePickerOptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  imagePickerOptionSubtitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  imagePickerCancelButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 25,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  imagePickerCancelText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EditItemScreen;
