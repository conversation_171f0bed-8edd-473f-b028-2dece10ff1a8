{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,375,472,584,669,770,884,965,1044,1135,1228,1321,1415,1521,1614,1709,1804,1895,1989,2070,2180,2287,2384,2493,2593,2696,2851,11042", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "370,467,579,664,765,879,960,1039,1130,1223,1316,1410,1516,1609,1704,1799,1890,1984,2065,2175,2282,2379,2488,2588,2691,2846,2944,11118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "34,35,36,37,38,39,40,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3028,3123,3230,3327,3427,3530,3634,11123", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3118,3225,3322,3422,3525,3629,3740,11219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "62,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "6187,6500,6601,6707", "endColumns": "103,100,105,100", "endOffsets": "6286,6596,6702,6803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "127", "startColumns": "4", "startOffsets": "11224", "endColumns": "75", "endOffsets": "11295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5013", "endColumns": "128", "endOffsets": "5137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,304,401,516,598,663,751,815,876,966,1029,1091,1159,1223,1279,1402,1467,1529,1585,1656,1783,1867,1951,2057,2134,2211,2298,2365,2431,2507,2587,2676,2743,2817,2887,2953,3039,3109,3200,3290,3364,3437,3526,3577,3649,3730,3816,3878,3942,4005,4119,4222,4330,4433", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "220,299,396,511,593,658,746,810,871,961,1024,1086,1154,1218,1274,1397,1462,1524,1580,1651,1778,1862,1946,2052,2129,2206,2293,2360,2426,2502,2582,2671,2738,2812,2882,2948,3034,3104,3195,3285,3359,3432,3521,3572,3644,3725,3811,3873,3937,4000,4114,4217,4325,4428,4508"}, "to": {"startLines": "2,33,41,42,43,64,65,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2949,3745,3842,3957,6347,6412,7104,7168,7229,7319,7382,7444,7512,7576,7632,7755,7820,7882,7938,8009,8136,8220,8304,8410,8487,8564,8651,8718,8784,8860,8940,9029,9096,9170,9240,9306,9392,9462,9553,9643,9717,9790,9879,9930,10002,10083,10169,10231,10295,10358,10472,10575,10683,10786", "endLines": "5,33,41,42,43,64,65,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "270,3023,3837,3952,4034,6407,6495,7163,7224,7314,7377,7439,7507,7571,7627,7750,7815,7877,7933,8004,8131,8215,8299,8405,8482,8559,8646,8713,8779,8855,8935,9024,9091,9165,9235,9301,9387,9457,9548,9638,9712,9785,9874,9925,9997,10078,10164,10226,10290,10353,10467,10570,10678,10781,10861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10b099e99495a469d20423a3ae446b36\\transformed\\android-image-cropper-4.6.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,159,227,294,355,407,462,526", "endColumns": "55,47,67,66,60,51,54,63,56", "endOffsets": "106,154,222,289,350,402,457,521,578"}, "to": {"startLines": "63,69,70,71,72,73,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6291,6808,6856,6924,6991,7052,10866,10921,10985", "endColumns": "55,47,67,66,60,51,54,63,56", "endOffsets": "6342,6851,6919,6986,7047,7099,10916,10980,11037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4039,4145,4299,4424,4533,4674,4799,4908,5142,5296,5402,5559,5685,5827,5981,6045,6108", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "4140,4294,4419,4528,4669,4794,4903,5008,5291,5397,5554,5680,5822,5976,6040,6103,6182"}}]}]}