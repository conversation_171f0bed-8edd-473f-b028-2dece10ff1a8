{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "72", "endOffsets": "274"}, "to": {"startLines": "143", "startColumns": "4", "startOffsets": "12642", "endColumns": "76", "endOffsets": "12714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45da6d36d3d2ef1e836ae57a6da35b9a\\transformed\\react-android-0.79.2-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,342,421,488,555,629,705,785,866,934,1013,1091,1166,1245,1325,1405,1476,1547,1646,1718,1793,1862", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "119,198,269,337,416,483,550,624,700,780,861,929,1008,1086,1161,1240,1320,1400,1471,1542,1641,1713,1788,1857,1930"}, "to": {"startLines": "33,45,70,72,73,75,88,89,90,125,126,127,128,130,131,132,133,134,135,136,137,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2956,4128,6949,7087,7155,7293,8252,8319,8393,11230,11310,11391,11459,11619,11697,11772,11851,11931,12011,12082,12153,12353,12425,12500,12569", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "3020,4202,7015,7150,7229,7355,8314,8388,8464,11305,11386,11454,11533,11692,11767,11846,11926,12006,12077,12148,12247,12420,12495,12564,12637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "64,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6383,6638,6739,6848", "endColumns": "100,100,108,100", "endOffsets": "6479,6734,6843,6944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,380,484,591,678,778,898,976,1053,1144,1237,1332,1426,1526,1619,1714,1808,1899,1990,2070,2176,2277,2374,2483,2583,2693,2853,11538", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "375,479,586,673,773,893,971,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2065,2171,2272,2369,2478,2578,2688,2848,2951,11614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "35,36,37,38,39,40,41,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3107,3201,3304,3401,3503,3605,3703,12252", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3196,3299,3396,3498,3600,3698,3820,12348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5189", "endColumns": "146", "endOffsets": "5331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,305,404,524,608,671,762,829,888,978,1043,1107,1176,1238,1292,1407,1465,1526,1580,1653,1780,1866,1950,2053,2128,2204,2290,2357,2423,2496,2576,2661,2732,2808,2887,2956,3052,3130,3225,3321,3395,3470,3569,3620,3687,3774,3864,3926,3990,4053,4155,4260,4357,4463", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "218,300,399,519,603,666,757,824,883,973,1038,1102,1171,1233,1287,1402,1460,1521,1575,1648,1775,1861,1945,2048,2123,2199,2285,2352,2418,2491,2571,2656,2727,2803,2882,2951,3047,3125,3220,3316,3390,3465,3564,3615,3682,3769,3859,3921,3985,4048,4150,4255,4352,4458,4536"}, "to": {"startLines": "2,34,42,43,44,65,66,71,74,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3825,3924,4044,6484,6547,7020,7234,7360,7450,7515,7579,7648,7710,7764,7879,7937,7998,8052,8125,8469,8555,8639,8742,8817,8893,8979,9046,9112,9185,9265,9350,9421,9497,9576,9645,9741,9819,9914,10010,10084,10159,10258,10309,10376,10463,10553,10615,10679,10742,10844,10949,11046,11152", "endLines": "5,34,42,43,44,65,66,71,74,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "268,3102,3919,4039,4123,6542,6633,7082,7288,7445,7510,7574,7643,7705,7759,7874,7932,7993,8047,8120,8247,8550,8634,8737,8812,8888,8974,9041,9107,9180,9260,9345,9416,9492,9571,9640,9736,9814,9909,10005,10079,10154,10253,10304,10371,10458,10548,10610,10674,10737,10839,10944,11041,11147,11225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4207,4315,4481,4606,4718,4856,4978,5089,5336,5484,5592,5756,5881,6024,6174,6235,6301", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "4310,4476,4601,4713,4851,4973,5084,5184,5479,5587,5751,5876,6019,6169,6230,6296,6378"}}]}]}