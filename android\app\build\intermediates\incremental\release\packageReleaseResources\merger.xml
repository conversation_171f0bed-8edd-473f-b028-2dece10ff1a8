<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\main\res"><file name="ic_launcher_background" path="D:\app\StyleApp\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="D:\app\StyleApp\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="splashscreen_logo" path="D:\app\StyleApp\android\app\src\main\res\drawable-hdpi\splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\app\StyleApp\android\app\src\main\res\drawable-mdpi\splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\app\StyleApp\android\app\src\main\res\drawable-xhdpi\splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\app\StyleApp\android\app\src\main\res\drawable-xxhdpi\splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\app\StyleApp\android\app\src\main\res\drawable-xxxhdpi\splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\app\StyleApp\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\app\StyleApp\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app\StyleApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\app\StyleApp\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="splashscreen_background">#ffffff</color><color name="iconBackground">#ffffff</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#ffffff</color></file><file path="D:\app\StyleApp\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SwipeSense</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string><string name="expo_runtime_version">exposdk:53.0.0</string></file><file path="D:\app\StyleApp\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style><style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style></file><file path="D:\app\StyleApp\android\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\build\generated\res\resValues\release"/><source path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets"/><source path="D:\app\StyleApp\android\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\build\generated\res\resValues\release"><file path="D:\app\StyleApp\android\app\build\generated\res\resValues\release\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source><source path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets"><file name="node_modules_reactnavigation_elements_src_assets_backicon" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-hdpi\node_modules_reactnavigation_elements_src_assets_backicon.png" qualifiers="hdpi-v4" type="drawable"/><file name="assets_animations_heart" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_animations_heart.png" qualifiers="mdpi-v4" type="drawable"/><file name="assets_animations_thumbs" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_animations_thumbs.png" qualifiers="mdpi-v4" type="drawable"/><file name="assets_welcome_bg" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_welcome_bg.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_src_assets_backicon" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_src_assets_backicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_src_assets_backiconmask" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_src_assets_backiconmask.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_src_assets_backicon" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_src_assets_backicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_src_assets_backicon" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_src_assets_backicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_src_assets_backicon" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_src_assets_backicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="keep" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\keep.xml" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_brands" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_brands.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_regular" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_regular.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_solid" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_solid.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons.ttf" qualifiers="" type="raw"/><file name="node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial" path="D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial.ttf" qualifiers="" type="raw"/></source><source path="D:\app\StyleApp\android\app\build\generated\res\processReleaseGoogleServices"><file path="D:\app\StyleApp\android\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">900945998905-nk13kuphejntned3ijhtbkm9o25i6tj7.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">900945998905</string><string name="google_api_key" translatable="false">AIzaSyDetCH2HxnF4mNBNIoLF2caqC0UI_8IbfE</string><string name="google_app_id" translatable="false">1:900945998905:android:5d82969b0911af703a49e8</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDetCH2HxnF4mNBNIoLF2caqC0UI_8IbfE</string><string name="google_storage_bucket" translatable="false">styleswipe-67cb3.firebasestorage.app</string><string name="project_id" translatable="false">styleswipe-67cb3</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets" generated-set="res-createBundleReleaseJsAndAssets$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>