import React, { useState } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ResponsiveText from './ResponsiveText';
import theme from '../utils/theme';
import { scaleWidth, scaleHeight } from '../utils/responsiveUtils';
import { createInputStyle } from '../utils/platformUtils';

/**
 * ResponsiveInput component that scales based on screen dimensions
 * and applies platform-specific optimizations
 * 
 * @param {Object} props - Component props
 * @param {string} props.label - Input label
 * @param {string} props.value - Input value
 * @param {Function} props.onChangeText - Text change handler
 * @param {string} props.placeholder - Input placeholder
 * @param {boolean} props.secureTextEntry - Whether input is for password
 * @param {string} props.error - Error message
 * @param {boolean} props.multiline - Whether input is multiline
 * @param {number} props.numberOfLines - Number of lines for multiline input
 * @param {string} props.leftIconName - Ionicons name for left icon
 * @param {string} props.rightIconName - Ionicons name for right icon
 * @param {Function} props.onRightIconPress - Right icon press handler
 * @param {boolean} props.autoCapitalize - Auto capitalize behavior
 * @param {string} props.keyboardType - Keyboard type
 * @param {boolean} props.editable - Whether input is editable
 * @param {Object} props.style - Additional styles for the input container
 * @param {Object} props.inputStyle - Additional styles for the TextInput
 * @param {Object} props.rest - Additional props for the TextInput
 * @returns {React.ReactElement} - ResponsiveInput component
 */
const ResponsiveInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  error,
  multiline = false,
  numberOfLines = 4,
  leftIconName,
  rightIconName,
  onRightIconPress,
  autoCapitalize = 'none',
  keyboardType = 'default',
  editable = true,
  style,
  inputStyle,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  
  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };
  
  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
  };
  
  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
  };
  
  // Determine container style based on state
  const getContainerStyle = () => {
    if (error) return styles.errorContainer;
    if (isFocused) return styles.focusedContainer;
    if (!editable) return styles.disabledContainer;
    return {};
  };
  
  // Determine input style based on state
  const getInputStyle = () => {
    if (!editable) return styles.disabledInput;
    return {};
  };
  
  return (
    <View style={[styles.wrapper, style]}>
      {label && (
        <ResponsiveText variant="label" style={styles.label}>
          {label}
        </ResponsiveText>
      )}
      
      <View style={[styles.container, getContainerStyle()]}>
        {leftIconName && (
          <Ionicons
            name={leftIconName}
            size={20}
            color={theme.colors.textLight}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#000"
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          multiline={multiline}
          numberOfLines={multiline ? numberOfLines : 1}
          autoCapitalize={autoCapitalize}
          keyboardType={keyboardType}
          editable={editable}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={[
            styles.input,
            leftIconName && styles.inputWithLeftIcon,
            (rightIconName || secureTextEntry) && styles.inputWithRightIcon,
            multiline && styles.multilineInput,
            getInputStyle(),
            inputStyle,
          ]}
          textAlignVertical={multiline ? 'top' : 'center'}
          {...rest}
        />
        
        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.rightIcon}
          >
            <Ionicons
              name={isPasswordVisible ? 'eye-off' : 'eye'}
              size={20}
              color={theme.colors.textLight}
            />
          </TouchableOpacity>
        )}
        
        {rightIconName && !secureTextEntry && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={styles.rightIcon}
            disabled={!onRightIconPress}
          >
            <Ionicons
              name={rightIconName}
              size={20}
              color={theme.colors.textLight}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <ResponsiveText variant="error" style={styles.errorText}>
          {error}
        </ResponsiveText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: scaleHeight(16),
    width: '100%',
  },
  label: {
    marginBottom: scaleHeight(6),
  },
  container: {
    ...createInputStyle({
      height: scaleHeight(50),
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borders.radius.md,
      paddingHorizontal: scaleWidth(15),
      backgroundColor: theme.colors.background,
    }),
    flexDirection: 'row',
    alignItems: 'center',
  },
  focusedContainer: {
    borderColor: theme.colors.primary,
  },
  errorContainer: {
    borderColor: theme.colors.error,
  },
  disabledContainer: {
    backgroundColor: theme.colors.backgroundLight,
    borderColor: theme.colors.borderDark,
  },
  input: {
    flex: 1,
    color: theme.colors.text,
    fontSize: theme.typography.fontSizes.md,
    paddingVertical: 0, // Important for Android
  },
  disabledInput: {
    color: theme.colors.textLighter,
  },
  inputWithLeftIcon: {
    paddingLeft: scaleWidth(8),
  },
  inputWithRightIcon: {
    paddingRight: scaleWidth(8),
  },
  multilineInput: {
    height: scaleHeight(100),
    paddingTop: scaleHeight(12),
  },
  leftIcon: {
    marginRight: scaleWidth(8),
  },
  rightIcon: {
    padding: scaleWidth(8),
  },
  errorText: {
    marginTop: scaleHeight(4),
  },
});

export default ResponsiveInput;
