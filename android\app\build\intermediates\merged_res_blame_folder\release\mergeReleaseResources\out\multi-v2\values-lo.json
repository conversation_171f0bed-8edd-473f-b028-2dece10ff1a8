{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,305,420,539,622,688,777,846,905,1000,1065,1123,1188,1249,1309,1415,1476,1536,1594,1665,1784,1870,1952,2065,2140,2216,2306,2373,2439,2508,2582,2661,2734,2811,2880,2950,3035,3110,3203,3296,3370,3439,3533,3585,3652,3736,3820,3882,3946,4009,4108,4200,4295,4387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "219,300,415,534,617,683,772,841,900,995,1060,1118,1183,1244,1304,1410,1471,1531,1589,1660,1779,1865,1947,2060,2135,2211,2301,2368,2434,2503,2577,2656,2729,2806,2875,2945,3030,3105,3198,3291,3365,3434,3528,3580,3647,3731,3815,3877,3941,4004,4103,4195,4290,4382,4461"}, "to": {"startLines": "2,33,41,42,43,64,65,69,72,74,75,76,77,78,79,80,81,82,83,84,85,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2941,3728,3843,3962,6473,6539,6941,7162,7288,7383,7448,7506,7571,7632,7692,7798,7859,7919,7977,8048,8234,8320,8402,8515,8590,8666,8756,8823,8889,8958,9032,9111,9184,9261,9330,9400,9485,9560,9653,9746,9820,9889,9983,10035,10102,10186,10270,10332,10396,10459,10558,10650,10745,10837", "endLines": "5,33,41,42,43,64,65,69,72,74,75,76,77,78,79,80,81,82,83,84,85,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "269,3017,3838,3957,4040,6534,6623,7005,7216,7378,7443,7501,7566,7627,7687,7793,7854,7914,7972,8043,8162,8315,8397,8510,8585,8661,8751,8818,8884,8953,9027,9106,9179,9256,9325,9395,9480,9555,9648,9741,9815,9884,9978,10030,10097,10181,10265,10327,10391,10454,10553,10645,10740,10832,10911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "11241", "endColumns": "75", "endOffsets": "11312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45da6d36d3d2ef1e836ae57a6da35b9a\\transformed\\react-android-0.79.2-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "44,70,71,73,86,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4045,7010,7080,7221,8167,10998,11069", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "4116,7075,7157,7283,8229,11064,11135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5137", "endColumns": "131", "endOffsets": "5264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4121,4229,4399,4529,4636,4788,4913,5021,5269,5421,5526,5692,5821,5992,6153,6221,6290", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "4224,4394,4524,4631,4783,4908,5016,5132,5416,5521,5687,5816,5987,6148,6216,6285,6370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,377,480,593,678,782,893,971,1048,1139,1232,1324,1418,1518,1611,1706,1802,1893,1984,2065,2172,2276,2374,2477,2581,2685,2842,10916", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "372,475,588,673,777,888,966,1043,1134,1227,1319,1413,1513,1606,1701,1797,1888,1979,2060,2167,2271,2369,2472,2576,2680,2837,2936,10993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "34,35,36,37,38,39,40,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3022,3118,3221,3320,3418,3519,3617,11140", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3113,3216,3315,3413,3514,3612,3723,11236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "63,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "6375,6628,6726,6841", "endColumns": "97,97,114,99", "endOffsets": "6468,6721,6836,6936"}}]}]}