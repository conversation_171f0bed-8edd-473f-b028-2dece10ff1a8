import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar // Added StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { useFocusEffect } from '@react-navigation/native';

const AddressScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [street, setStreet] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [country, setCountry] = useState('India');
  const [phone, setPhone] = useState('');

  const currentUserId = auth.currentUser?.uid;

  useEffect(() => {
    fetchUserAddress();
  }, []);

  const fetchUserAddress = async () => {
    if (!currentUserId) {
      setLoading(false);
      return;
    }

    try {
      const userDocRef = doc(db, 'users', currentUserId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.address) {
          setStreet(userData.address.street || '');
          setCity(userData.address.city || '');
          setState(userData.address.state || '');
          setZipCode(userData.address.zipCode || '');
          setCountry(userData.address.country || 'India');
          setPhone(userData.address.phone || '');
        }
      }
    } catch (error) {
      console.error('Error fetching user address:', error);
      Alert.alert('Error', 'Failed to load your address information');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!street.trim()) {
      Alert.alert('Missing Information', 'Please enter your street address');
      return false;
    }
    if (!city.trim()) {
      Alert.alert('Missing Information', 'Please enter your city');
      return false;
    }
    if (!state.trim()) {
      Alert.alert('Missing Information', 'Please enter your state');
      return false;
    }
    if (!zipCode.trim()) {
      Alert.alert('Missing Information', 'Please enter your ZIP/Postal code');
      return false;
    }
    if (!country.trim()) {
      Alert.alert('Missing Information', 'Please enter your country');
      return false;
    }
    if (!phone.trim()) {
      Alert.alert('Missing Information', 'Please enter your phone number');
      return false;
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(phone.trim())) {
      Alert.alert('Invalid Phone', 'Please enter a valid 10-digit phone number');
      return false;
    }

    return true;
  };

  const saveAddress = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      const userDocRef = doc(db, 'users', currentUserId);

      await updateDoc(userDocRef, {
        address: {
          street: street.trim(),
          city: city.trim(),
          state: state.trim(),
          zipCode: zipCode.trim(),
          country: country.trim(),
          phone: phone.trim()
        }
      });

      // Dispatch an event that the address was updated
      navigation.setParams({ addressUpdated: true });

      Alert.alert(
        'Success',
        'Your address has been saved',
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error saving address:', error);
      Alert.alert('Error', 'Failed to save your address');
    } finally {
      setSaving(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Shipping Address</Text>
          <View style={styles.rightPlaceholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading address information...</Text>
          </View>
        ) : (
          <ScrollView style={styles.formContainer} contentContainerStyle={styles.formContent}>
            <Text style={styles.label}>Street Address</Text>
            <TextInput
              style={styles.input}
              value={street}
              onChangeText={setStreet}
              placeholder="Enter your street address"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <Text style={styles.label}>City</Text>
            <TextInput
              style={styles.input}
              value={city}
              onChangeText={setCity}
              placeholder="Enter your city"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <Text style={styles.label}>State</Text>
            <TextInput
              style={styles.input}
              value={state}
              onChangeText={setState}
              placeholder="Enter your state"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <Text style={styles.label}>ZIP/Postal Code</Text>
            <TextInput
              style={styles.input}
              value={zipCode}
              onChangeText={setZipCode}
              placeholder="Enter your ZIP/Postal code"
              keyboardType="numeric"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <Text style={styles.label}>Country</Text>
            <TextInput
              style={styles.input}
              value={country}
              onChangeText={setCountry}
              placeholder="Enter your country"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={styles.input}
              value={phone}
              onChangeText={setPhone}
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
              placeholderTextColor="#000"
              editable={!saving}
            />

            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveAddress}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save Address</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 // Added for Android
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    // Removed elevation for Android, handled by safeArea paddingTop
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  rightPlaceholder: {
    width: 34,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 16,
  },
  formContainer: {
    flex: 1,
  },
  formContent: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  saveButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 30,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AddressScreen;
