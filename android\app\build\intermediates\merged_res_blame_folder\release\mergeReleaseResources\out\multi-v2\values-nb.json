{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "11085", "endColumns": "74", "endOffsets": "11155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "62,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "6225,6548,6649,6761", "endColumns": "109,100,111,96", "endOffsets": "6330,6644,6756,6853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,303,402,521,603,667,759,827,887,974,1036,1100,1168,1233,1287,1396,1454,1516,1570,1645,1765,1847,1927,2031,2109,2189,2277,2344,2410,2478,2552,2642,2713,2791,2861,2931,3020,3098,3186,3276,3348,3420,3504,3555,3621,3702,3785,3847,3911,3974,4074,4172,4265,4363", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "211,298,397,516,598,662,754,822,882,969,1031,1095,1163,1228,1282,1391,1449,1511,1565,1640,1760,1842,1922,2026,2104,2184,2272,2339,2405,2473,2547,2637,2708,2786,2856,2926,3015,3093,3181,3271,3343,3415,3499,3550,3616,3697,3780,3842,3906,3969,4069,4167,4260,4358,4436"}, "to": {"startLines": "2,33,41,42,43,64,65,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2898,3711,3810,3929,6392,6456,7158,7226,7286,7373,7435,7499,7567,7632,7686,7795,7853,7915,7969,8044,8164,8246,8326,8430,8508,8588,8676,8743,8809,8877,8951,9041,9112,9190,9260,9330,9419,9497,9585,9675,9747,9819,9903,9954,10020,10101,10184,10246,10310,10373,10473,10571,10664,10762", "endLines": "5,33,41,42,43,64,65,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "261,2980,3805,3924,4006,6451,6543,7221,7281,7368,7430,7494,7562,7627,7681,7790,7848,7910,7964,8039,8159,8241,8321,8425,8503,8583,8671,8738,8804,8872,8946,9036,9107,9185,9255,9325,9414,9492,9580,9670,9742,9814,9898,9949,10015,10096,10179,10241,10305,10368,10468,10566,10659,10757,10835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "34,35,36,37,38,39,40,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2985,3079,3181,3278,3377,3485,3591,10984", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3074,3176,3273,3372,3480,3586,3706,11080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10b099e99495a469d20423a3ae446b36\\transformed\\android-image-cropper-4.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,112,156,222,286,359,412", "endColumns": "56,43,65,63,72,52,63", "endOffsets": "107,151,217,281,354,407,471"}, "to": {"startLines": "63,69,70,71,72,73,122", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6335,6858,6902,6968,7032,7105,10840", "endColumns": "56,43,65,63,72,52,63", "endOffsets": "6387,6897,6963,7027,7100,7153,10899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4011,4117,4276,4402,4511,4667,4797,4917,5150,5304,5411,5572,5700,5842,6018,6085,6147", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4112,4271,4397,4506,4662,4792,4912,5015,5299,5406,5567,5695,5837,6013,6080,6142,6220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "266,369,464,578,664,764,877,954,1029,1120,1213,1307,1401,1501,1594,1689,1787,1878,1969,2047,2150,2248,2344,2448,2547,2648,2801,10904", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "364,459,573,659,759,872,949,1024,1115,1208,1302,1396,1496,1589,1684,1782,1873,1964,2042,2145,2243,2339,2443,2542,2643,2796,2893,10979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5020", "endColumns": "129", "endOffsets": "5145"}}]}]}