{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "34,35,36,37,38,39,40,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3111,3208,3310,3411,3508,3615,3723,11130", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3203,3305,3406,3503,3610,3718,3840,11226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,396,504,617,705,811,926,1006,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2323,2424,2534,2652,2760,2923,11045", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "391,499,612,700,806,921,1001,1078,1169,1262,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2318,2419,2529,2647,2755,2918,3020,11125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "62,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6533,6800,6905,7016", "endColumns": "102,104,110,104", "endOffsets": "6631,6900,7011,7116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "78", "endOffsets": "280"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "11231", "endColumns": "82", "endOffsets": "11309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,235,321,426,562,647,712,811,879,938,1027,1094,1157,1232,1300,1354,1474,1532,1594,1648,1723,1865,1955,2040,2155,2239,2322,2418,2485,2551,2625,2703,2794,2868,2947,3020,3092,3196,3269,3368,3468,3542,3617,3724,3776,3843,3934,4028,4090,4154,4217,4336,4438,4547,4650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "230,316,421,557,642,707,806,874,933,1022,1089,1152,1227,1295,1349,1469,1527,1589,1643,1718,1860,1950,2035,2150,2234,2317,2413,2480,2546,2620,2698,2789,2863,2942,3015,3087,3191,3264,3363,3463,3537,3612,3719,3771,3838,3929,4023,4085,4149,4212,4331,4433,4542,4645,4730"}, "to": {"startLines": "2,33,41,42,43,63,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3845,3950,4086,6636,6701,7121,7189,7248,7337,7404,7467,7542,7610,7664,7784,7842,7904,7958,8033,8175,8265,8350,8465,8549,8632,8728,8795,8861,8935,9013,9104,9178,9257,9330,9402,9506,9579,9678,9778,9852,9927,10034,10086,10153,10244,10338,10400,10464,10527,10646,10748,10857,10960", "endLines": "5,33,41,42,43,63,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "280,3106,3945,4081,4166,6696,6795,7184,7243,7332,7399,7462,7537,7605,7659,7779,7837,7899,7953,8028,8170,8260,8345,8460,8544,8627,8723,8790,8856,8930,9008,9099,9173,9252,9325,9397,9501,9574,9673,9773,9847,9922,10029,10081,10148,10239,10333,10395,10459,10522,10641,10743,10852,10955,11040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4278,4454,4592,4701,4859,4995,5117,5375,5554,5661,5839,5977,6139,6318,6386,6452", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "4273,4449,4587,4696,4854,4990,5112,5225,5549,5656,5834,5972,6134,6313,6381,6447,6528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5230", "endColumns": "144", "endOffsets": "5370"}}]}]}