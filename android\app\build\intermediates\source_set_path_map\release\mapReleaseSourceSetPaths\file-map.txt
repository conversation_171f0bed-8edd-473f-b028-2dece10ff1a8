com.swipesense.swipesense.app-fragment-1.6.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\res
com.swipesense.swipesense.app-lifecycle-runtime-2.6.2-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\res
com.swipesense.swipesense.app-room-runtime-2.6.1-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\res
com.swipesense.swipesense.app-android-image-cropper-4.6.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\res
com.swipesense.swipesense.app-appcompat-resources-1.7.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0\res
com.swipesense.swipesense.app-activity-ktx-1.10.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0\res
com.swipesense.swipesense.app-profileinstaller-1.4.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\res
com.swipesense.swipesense.app-core-1.13.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\res
com.swipesense.swipesense.app-core-runtime-2.2.0-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0\res
com.swipesense.swipesense.app-coordinatorlayout-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\res
com.swipesense.swipesense.app-core-ktx-1.13.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1\res
com.swipesense.swipesense.app-savedstate-ktx-1.2.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1\res
com.swipesense.swipesense.app-firebase-messaging-24.0.1-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\res
com.swipesense.swipesense.app-startup-runtime-1.1.1-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\res
com.swipesense.swipesense.app-annotation-experimental-1.4.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0\res
com.swipesense.swipesense.app-emoji2-1.3.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\res
com.swipesense.swipesense.app-cardview-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0\res
com.swipesense.swipesense.app-tracing-ktx-1.2.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0\res
com.swipesense.swipesense.app-room-ktx-2.6.1-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1\res
com.swipesense.swipesense.app-react-android-0.79.2-release-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\res
com.swipesense.swipesense.app-sqlite-2.4.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0\res
com.swipesense.swipesense.app-play-services-basement-18.3.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\res
com.swipesense.swipesense.app-drawee-3.6.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0\res
com.swipesense.swipesense.app-material-1.6.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\res
com.swipesense.swipesense.app-glide-4.16.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\res
com.swipesense.swipesense.app-lifecycle-viewmodel-ktx-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.swipesense.swipesense.app-fragment-ktx-1.6.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1\res
com.swipesense.swipesense.app-lifecycle-process-2.6.2-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\res
com.swipesense.swipesense.app-expo.modules.imagepicker-16.1.4-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\res
com.swipesense.swipesense.app-transition-1.2.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\res
com.swipesense.swipesense.app-lifecycle-livedata-core-2.6.2-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2\res
com.swipesense.swipesense.app-play-services-wallet-18.1.3-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3\res
com.swipesense.swipesense.app-play-services-base-18.3.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\res
com.swipesense.swipesense.app-viewpager2-1.0.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0\res
com.swipesense.swipesense.app-autofill-1.1.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0\res
com.swipesense.swipesense.app-savedstate-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\res
com.swipesense.swipesense.app-sqlite-framework-2.4.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0\res
com.swipesense.swipesense.app-lifecycle-viewmodel-2.6.2-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\res
com.swipesense.swipesense.app-browser-1.6.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0\res
com.swipesense.swipesense.app-lifecycle-livedata-core-ktx-2.6.2-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.swipesense.swipesense.app-lottie-6.5.2-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\res
com.swipesense.swipesense.app-tracing-1.2.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0\res
com.swipesense.swipesense.app-recyclerview-1.1.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\res
com.swipesense.swipesense.app-firebase-common-21.0.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\res
com.swipesense.swipesense.app-activity-1.10.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0\res
com.swipesense.swipesense.app-drawerlayout-1.1.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1\res
com.swipesense.swipesense.app-play-services-auth-21.1.0-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\res
com.swipesense.swipesense.app-appcompat-1.7.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\res
com.swipesense.swipesense.app-constraintlayout-2.0.1-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\res
com.swipesense.swipesense.app-lifecycle-livedata-2.6.2-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2\res
com.swipesense.swipesense.app-lifecycle-service-2.6.2-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2\res
com.swipesense.swipesense.app-media-1.0.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\res
com.swipesense.swipesense.app-play-services-maps-17.0.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\res
com.swipesense.swipesense.app-standard-core-1.6.50-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\res
com.swipesense.swipesense.app-lifecycle-viewmodel-savedstate-2.6.2-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.swipesense.swipesense.app-emoji2-views-helper-1.3.0-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0\res
com.swipesense.swipesense.app-lifecycle-runtime-ktx-2.6.2-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2\res
com.swipesense.swipesense.app-swiperefreshlayout-1.1.0-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0\res
com.swipesense.swipesense.app-res-58 D:\app\StyleApp\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.swipesense.swipesense.app-pngs-59 D:\app\StyleApp\android\app\build\generated\res\pngs\release
com.swipesense.swipesense.app-res-60 D:\app\StyleApp\android\app\build\generated\res\processReleaseGoogleServices
com.swipesense.swipesense.app-resValues-61 D:\app\StyleApp\android\app\build\generated\res\resValues\release
com.swipesense.swipesense.app-packageReleaseResources-62 D:\app\StyleApp\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.swipesense.swipesense.app-packageReleaseResources-63 D:\app\StyleApp\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.swipesense.swipesense.app-release-64 D:\app\StyleApp\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.swipesense.swipesense.app-main-65 D:\app\StyleApp\android\app\src\main\res
com.swipesense.swipesense.app-release-66 D:\app\StyleApp\android\app\src\release\res
com.swipesense.swipesense.app-release-67 D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-68 D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-69 D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-70 D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-71 D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-72 D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-73 D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-74 D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-75 D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-76 D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-77 D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-78 D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-79 D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-80 D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-81 D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-82 D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-83 D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-84 D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-85 D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-86 D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-87 D:\app\StyleApp\node_modules\expo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-88 D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-89 D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-90 D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-91 D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-92 D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-93 D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.swipesense.swipesense.app-release-94 D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
