[{"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xxhdpi_node_modules_reactnavigation_elements_src_assets_backicon.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-xxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable_ic_launcher_background.xml.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable/ic_launcher_background.xml"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_assets_welcome_bg.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-mdpi/assets_welcome_bg.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_regular.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_regular.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xhdpi_node_modules_reactnavigation_elements_src_assets_backicon.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-xhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_node_modules_reactnavigation_elements_src_assets_backicon.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backicon.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_brands.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_brands.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_node_modules_reactnavigation_elements_src_assets_backiconmask.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backiconmask.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-hdpi_node_modules_reactnavigation_elements_src_assets_backicon.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-hdpi/node_modules_reactnavigation_elements_src_assets_backicon.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable_rn_edit_text_material.xml.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable/rn_edit_text_material.xml"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xxxhdpi_node_modules_reactnavigation_elements_src_assets_backicon.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-xxxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_assets_animations_heart.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-mdpi/assets_animations_heart.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_keep.xml.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/keep.xml"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.swipesense.swipesense.app-main-65:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.swipesense.swipesense.app-release-64:/raw_node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_solid.ttf.flat", "source": "com.swipesense.swipesense.app-res-58:/raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_solid.ttf"}, {"merged": "com.swipesense.swipesense.app-release-64:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.swipesense.swipesense.app-main-65:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.swipesense.swipesense.app-release-64:/drawable-mdpi_assets_animations_thumbs.png.flat", "source": "com.swipesense.swipesense.app-res-58:/drawable-mdpi/assets_animations_thumbs.png"}]