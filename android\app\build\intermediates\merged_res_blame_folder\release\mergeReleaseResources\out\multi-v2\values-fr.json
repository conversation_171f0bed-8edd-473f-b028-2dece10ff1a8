{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "35,36,37,38,39,40,41,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3183,3281,3383,3482,3584,3688,3792,13227", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3276,3378,3477,3579,3683,3787,3905,13323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4306,4412,4592,4722,4831,5002,5135,5256,5534,5712,5824,6009,6145,6305,6484,6557,6624", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4407,4587,4717,4826,4997,5130,5251,5364,5707,5819,6004,6140,6300,6479,6552,6619,6703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "64,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6708,7042,7144,7263", "endColumns": "106,101,118,104", "endOffsets": "6810,7139,7258,7363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10b099e99495a469d20423a3ae446b36\\transformed\\android-image-cropper-4.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,168,246,320,383,447,508,584", "endColumns": "63,48,77,73,62,63,60,75,54", "endOffsets": "114,163,241,315,378,442,503,579,634"}, "to": {"startLines": "65,72,73,74,75,76,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6815,7438,7487,7565,7639,7702,11970,12031,12107", "endColumns": "63,48,77,73,62,63,60,75,54", "endOffsets": "6874,7482,7560,7634,7697,7761,12026,12102,12157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,514,624,706,812,942,1020,1096,1187,1280,1378,1473,1573,1666,1759,1854,1945,2036,2122,2232,2343,2446,2557,2665,2772,2931,12502", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,509,619,701,807,937,1015,1091,1182,1275,1373,1468,1568,1661,1754,1849,1940,2031,2117,2227,2338,2441,2552,2660,2767,2926,3025,12584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5369", "endColumns": "164", "endOffsets": "5529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45da6d36d3d2ef1e836ae57a6da35b9a\\transformed\\react-android-0.79.2-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "33,45,71,78,80,93,94,132,133,134,135,137,138,139,140,141,142,143,144,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,4223,7368,7849,7998,9005,9084,12162,12252,12344,12415,12589,12664,12751,12831,12911,12986,13063,13136,13328,13407,13488,13560", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3095,4301,7433,7927,8060,9079,9161,12247,12339,12410,12497,12659,12746,12826,12906,12981,13058,13131,13222,13402,13483,13555,13635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a985f938b8347b0559f71072f80100a\\transformed\\play-services-wallet-18.1.3\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "74", "endOffsets": "276"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "13640", "endColumns": "78", "endOffsets": "13714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3287,3379,3471,3545,3629,3721,3778,3844,3927,4014,4076,4140,4203,4305,4403,4500,4601", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3282,3374,3466,3540,3624,3716,3773,3839,3922,4009,4071,4135,4198,4300,4398,4495,4596,4685"}, "to": {"startLines": "2,34,42,43,44,66,67,77,79,81,82,83,84,85,86,87,88,89,90,91,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3100,3910,4008,4138,6879,6945,7766,7932,8065,8167,8242,8298,8377,8437,8491,8613,8672,8734,8788,8870,9166,9258,9342,9456,9535,9616,9709,9776,9842,9922,10003,10106,10179,10257,10330,10402,10495,10567,10659,10751,10825,10909,11001,11058,11124,11207,11294,11356,11420,11483,11585,11683,11780,11881", "endLines": "5,34,42,43,44,66,67,77,79,81,82,83,84,85,86,87,88,89,90,91,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "283,3178,4003,4133,4218,6940,7037,7844,7993,8162,8237,8293,8372,8432,8486,8608,8667,8729,8783,8865,9000,9253,9337,9451,9530,9611,9704,9771,9837,9917,9998,10101,10174,10252,10325,10397,10490,10562,10654,10746,10820,10904,10996,11053,11119,11202,11289,11351,11415,11478,11580,11678,11775,11876,11965"}}]}]}