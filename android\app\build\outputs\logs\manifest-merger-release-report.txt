-- Merging decision tree log ---
manifest
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:1-36:12
MERGED from [:react-native-google-signin_google-signin] D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:lottie-react-native] D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-picker_picker] D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] D:\app\StyleApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fast-image] D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-reanimated] D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu] D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b80d12792d1a7be2256f4e37b58b60ad\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\AndroidManifest.xml:2:1-12:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [:expo-structured-headers] D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:2:1-57:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:2:1-36:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60b9f2d453f2a1c09171c19bd0ac70a8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [:expo-image-loader] D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:2:1-72:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa7089c0ee765e01549bc3392fb0006c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-43:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e22240d77ee0f4d85702fcf824e14a5\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e01052569e02ab0291c0f03331c929c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e37d945e8cdd593d153ae6b326930c29\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ac8bc596cbb547332dca89c376d1c0\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd954f90d8f2c4273306f49664d2061\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bef216feedfe7a33c874b14fbc22432\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0234a2f446045858446afb54e99c4f6\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f718ba2aef815ea123dd94a0b7b11a6b\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b4a880772cc9db84cee2b1e2b17462\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db61630674b5354a7461fad1c3e11e92\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8162ad386fe5e0976b51bde6cd6752c9\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86df225c801f04dd83f966fb7199d290\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2580c5f00df3cbf3046df8e7aa72b729\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b1677671948d60be2dc241555c23bb2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45e378629d6a6ab396b8e8b880ed6a5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58bc3c894ad84d2b65f15f4a016489f4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0ae2db523f9adde7bccdd47cf308816\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ba28e1a6fd30e6187724d2984890b19\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d955b3d60156daa82f0383953fd510\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\446f3c9390a97aac50fe98270f678820\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c157f06c0f96b582066a5b9eb599959\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cb820fc5db64a7525eeb2682ddcf720\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f09bb2d5dc35eb4e928e272f6dca30c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e263a4dd960e4588d23a27736a1b7b15\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee50b4de2b12b944cfbb2daa7bde582\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb85cff1fb455cfeb853e7d28becd57\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2da426d8ee806ec16571263a538041fb\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e62c7fb3c64d75ccde48e1655bf5c0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\734ae7c8352c0532597a0ebf552cab00\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9657e101c69ac8a3aaf5e8c2b267790\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [:expo-constants] D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-eas-client] D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-manipulator] D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcbc3b5eb86a21cac52935b2937caaa\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7871d9d24c3f3746bb4a5fc40278656\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\928079e25932fbb6a7c41340fa8db348\transformed\expo.modules.crypto-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d234a62f821b30da3491134936d98eee\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\14367743b3d448efc6a1091e95859271\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9097b48713004c2258b0ee96e36ec8d\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed01b3e70387f3ba7d6a0aee0352e49\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45535a49377975f245c627db7f8fd4b9\transformed\hermes-android-0.79.2-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9145e54b32f16f48e4f4ff452aef51f4\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d61080a10e48df79fafacf9da4e70d8\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb7d108cd815b5dd21203251df132f2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c13d5e1dba0c5c90d48382ed009c4dd6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f398d430bfa01060ee6fa697657c61a7\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b3fb91b5227b84238b680dfb7e7bd3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7469a7f8a668e564f27933100b711f8f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a7ac7e8c682bcd03d2252707759078a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bccd564c4f2a425ba7527fe4eb35e1f\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
	package
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:4:3-68
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:4:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:5:3-75
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:5:20-73
uses-permission#android.permission.VIBRATE
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:6:3-63
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:6:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:7:3-78
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:7:20-76
uses-permission#android.permission.CAMERA
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:8:3-62
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:8:20-60
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:9:3-73
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:9:20-71
queries
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:10:3-16:13
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:10:5-39:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:11:5-15:14
action#android.intent.action.VIEW
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:7-58
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:7-67
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:17-65
data
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
	android:scheme
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
application
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:3-35:17
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:3-35:17
MERGED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:41:5-70:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-41:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:221-247
	android:label
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:48-80
	android:roundIcon
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:116-161
	android:icon
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:81-115
	android:allowBackup
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:162-188
	android:theme
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:189-220
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:5-83
	android:value
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:60-81
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:5-119
	android:value
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:73-117
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:5-105
	android:value
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:81-103
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:5-99
	android:value
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:80-97
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:16-79
activity#com.swipesense.swipesense.MainActivity
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:5-34:16
	android:screenOrientation
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:280-316
	android:launchMode
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:135-166
	android:windowSoftInputMode
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:167-209
	android:exported
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:256-279
	android:configChanges
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:44-134
	android:theme
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:210-255
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:23:7-26:23
action#android.intent.action.MAIN
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:9-60
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:25:9-68
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:25:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+swipesense+data:scheme:swipesense
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:27:7-33:23
category#android.intent.category.DEFAULT
ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:29:9-67
	android:name
		ADDED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:29:19-65
uses-sdk
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-google-signin_google-signin] D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\app\StyleApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\app\StyleApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fast-image] D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fast-image] D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b80d12792d1a7be2256f4e37b58b60ad\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b80d12792d1a7be2256f4e37b58b60ad\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45da6d36d3d2ef1e836ae57a6da35b9a\transformed\react-android-0.79.2-release\AndroidManifest.xml:10:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4c6936d97d71b0228d8c242453a157\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [:expo-structured-headers] D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9ad27e2f5f1b7d6aef8b0ce35d5a656\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11b1df42eb1ca482eeb54edc1bdf4f54\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6488334b9c3655ec56f031b42c75efd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d14ee28d9c902f7154f6bed3fc64fea0\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b03c7290e2ea8ab99a951f735da6b744\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60b9f2d453f2a1c09171c19bd0ac70a8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60b9f2d453f2a1c09171c19bd0ac70a8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [:expo-image-loader] D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcb94760b84da5724486a170cc06789\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4d6891c9b62852397bb8512a238aad\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a985f938b8347b0559f71072f80100a\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9107196f009692a6408a7c14714d040a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa7089c0ee765e01549bc3392fb0006c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa7089c0ee765e01549bc3392fb0006c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\992f5cd0e2396d792424e2a5becd4771\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7fa144a6ddd7f4faf47c17ad3f857c\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86dbf78576f4e6aeb8d94d2fc7dbc633\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b066a82796eacf55fc41c00bd8fbd169\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\294b2f255e9db25cb54d8ebcfc38c000\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\772055669d8ad0c65d7e85a6a5afa998\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e22240d77ee0f4d85702fcf824e14a5\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e22240d77ee0f4d85702fcf824e14a5\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\453dfcbaa3653b35751c597cbe78c39b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b682436a33d659157b91ad0ab62a909d\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2da5887438610709eebaf184e882fa\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2935fa3f8b5b8293c715c0c3fec09235\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e01052569e02ab0291c0f03331c929c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e01052569e02ab0291c0f03331c929c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\255f4942e2d4c71252a16ec92082f819\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe04e2bbd059ff1ff81fc4d2594c096b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9364385e2113e34f81f4734e7a255453\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e37d945e8cdd593d153ae6b326930c29\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e37d945e8cdd593d153ae6b326930c29\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ac8bc596cbb547332dca89c376d1c0\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ac8bc596cbb547332dca89c376d1c0\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd954f90d8f2c4273306f49664d2061\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd954f90d8f2c4273306f49664d2061\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bef216feedfe7a33c874b14fbc22432\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bef216feedfe7a33c874b14fbc22432\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0234a2f446045858446afb54e99c4f6\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0234a2f446045858446afb54e99c4f6\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f718ba2aef815ea123dd94a0b7b11a6b\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f718ba2aef815ea123dd94a0b7b11a6b\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b4a880772cc9db84cee2b1e2b17462\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b4a880772cc9db84cee2b1e2b17462\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54f0e64bdd64e064c737f6586e597698\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db61630674b5354a7461fad1c3e11e92\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db61630674b5354a7461fad1c3e11e92\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8162ad386fe5e0976b51bde6cd6752c9\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8162ad386fe5e0976b51bde6cd6752c9\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86df225c801f04dd83f966fb7199d290\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86df225c801f04dd83f966fb7199d290\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2580c5f00df3cbf3046df8e7aa72b729\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2580c5f00df3cbf3046df8e7aa72b729\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b1677671948d60be2dc241555c23bb2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b1677671948d60be2dc241555c23bb2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45e378629d6a6ab396b8e8b880ed6a5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45e378629d6a6ab396b8e8b880ed6a5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58bc3c894ad84d2b65f15f4a016489f4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58bc3c894ad84d2b65f15f4a016489f4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0ae2db523f9adde7bccdd47cf308816\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0ae2db523f9adde7bccdd47cf308816\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ba28e1a6fd30e6187724d2984890b19\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ba28e1a6fd30e6187724d2984890b19\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d955b3d60156daa82f0383953fd510\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d955b3d60156daa82f0383953fd510\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\446f3c9390a97aac50fe98270f678820\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\446f3c9390a97aac50fe98270f678820\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c157f06c0f96b582066a5b9eb599959\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c157f06c0f96b582066a5b9eb599959\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cb820fc5db64a7525eeb2682ddcf720\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cb820fc5db64a7525eeb2682ddcf720\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6341c42076e9ce2c9e438f16cdbbaab\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0308a3384f661323c8933cff8f3f983\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c14bff0aa9328a5d139e2dea3e48e1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88230107b054b3bb51685725ade087b1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbde07162a84b1aa93334e1d026569ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f09bb2d5dc35eb4e928e272f6dca30c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f09bb2d5dc35eb4e928e272f6dca30c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\883f736fdd696b14341d4f9a38ef6d83\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e263a4dd960e4588d23a27736a1b7b15\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e263a4dd960e4588d23a27736a1b7b15\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77f29fd7682aaa8b2e182141ce6bfb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c154009aca439b0cb55bd0193fb520\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee50b4de2b12b944cfbb2daa7bde582\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee50b4de2b12b944cfbb2daa7bde582\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb85cff1fb455cfeb853e7d28becd57\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb85cff1fb455cfeb853e7d28becd57\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2da426d8ee806ec16571263a538041fb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2da426d8ee806ec16571263a538041fb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2520eaf7a2a62647c2f5167851cf48\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e62c7fb3c64d75ccde48e1655bf5c0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e62c7fb3c64d75ccde48e1655bf5c0\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\734ae7c8352c0532597a0ebf552cab00\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\734ae7c8352c0532597a0ebf552cab00\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9657e101c69ac8a3aaf5e8c2b267790\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9657e101c69ac8a3aaf5e8c2b267790\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c19ea3c1c62522119af42ee94f97b3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb9042c2c0c83662b698ea24e87e4f2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a2eb2ed561d647bfbf4bcbfdd0b3d1\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbec9af0daf4f71de96e2b849c1dd9aa\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58a267c04da52cf46e5e14348fb90aa\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8895dbafaaa8bffd13b9d099f56e8419\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3add23e9a92a38428c64444d1045855\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5b4ce3c7792672f8739fba72ba30a0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f299f40845dfbe6d39c6822e76f9796d\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cfb2c872077fca3b3db4bf30de7b037\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6765068b589be840fbc6b21f0ed510ef\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\053c01a885a992c7c2f647f450c41206\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6be50c1984bcf96601402e4809c25a50\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [:expo-constants] D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-manipulator] D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-manipulator] D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcbc3b5eb86a21cac52935b2937caaa\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fcbc3b5eb86a21cac52935b2937caaa\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7871d9d24c3f3746bb4a5fc40278656\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7871d9d24c3f3746bb4a5fc40278656\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\928079e25932fbb6a7c41340fa8db348\transformed\expo.modules.crypto-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\928079e25932fbb6a7c41340fa8db348\transformed\expo.modules.crypto-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d234a62f821b30da3491134936d98eee\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d234a62f821b30da3491134936d98eee\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\14367743b3d448efc6a1091e95859271\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\14367743b3d448efc6a1091e95859271\transformed\expo.modules.lineargradient-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab4d2731f8235c7825c4c57d681124a3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\453a6ab1b35089766947a0caad733b57\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9097b48713004c2258b0ee96e36ec8d\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9097b48713004c2258b0ee96e36ec8d\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d31395d4b3c580007c20e23d39fcbc8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b99b97536cbccd86f149bc730ac3f46\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38715fc6f3907430a250a96bf40fcfeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed01b3e70387f3ba7d6a0aee0352e49\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed01b3e70387f3ba7d6a0aee0352e49\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45535a49377975f245c627db7f8fd4b9\transformed\hermes-android-0.79.2-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\45535a49377975f245c627db7f8fd4b9\transformed\hermes-android-0.79.2-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9145e54b32f16f48e4f4ff452aef51f4\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9145e54b32f16f48e4f4ff452aef51f4\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d61080a10e48df79fafacf9da4e70d8\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d61080a10e48df79fafacf9da4e70d8\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb7d108cd815b5dd21203251df132f2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb7d108cd815b5dd21203251df132f2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\440d9e3ca05410476e08eae94e039562\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c13d5e1dba0c5c90d48382ed009c4dd6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c13d5e1dba0c5c90d48382ed009c4dd6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fccb84a9ecc2e1b0cc797f46b7377c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f398d430bfa01060ee6fa697657c61a7\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f398d430bfa01060ee6fa697657c61a7\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c263540fae2c0643d6c5f17cf477f4e\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3876f7b2f9a7551f523fa3a6459a3719\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9d920d53d706423c40463f601441e54\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e8bd9d9933f9dd9db1f57e3a8163db\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b3fb91b5227b84238b680dfb7e7bd3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b3fb91b5227b84238b680dfb7e7bd3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7469a7f8a668e564f27933100b711f8f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7469a7f8a668e564f27933100b711f8f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a7ac7e8c682bcd03d2252707759078a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a7ac7e8c682bcd03d2252707759078a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bccd564c4f2a425ba7527fe4eb35e1f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bccd564c4f2a425ba7527fe4eb35e1f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\app\StyleApp\android\app\src\main\AndroidManifest.xml
activity#com.razorpay.CheckoutActivity
ADDED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:86
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:42:9-50:20
MERGED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-57
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\add3980feeeeeded72c78286e69f9bd1\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
	android:theme
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
	android:resource
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
category#android.intent.category.OPENABLE
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
	android:grantUriPermissions
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:28:9-30:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:37:21-58
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30264b12757f9f5b6cc9af296371db76\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.51] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae892a731fb8a3254279c2b5f17124a7\transformed\standard-core-1.6.51\AndroidManifest.xml:68:13-61
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14adcf5955e87db8a1a129000c3eb149\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.swipesense.swipesense.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.swipesense.swipesense.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
