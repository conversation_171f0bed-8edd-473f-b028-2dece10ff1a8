# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }

# Proguard rules for Razorpay and common issues
# Keep proguard.annotation.Keep and KeepClassMembers (fixes missing class error)
-keep class proguard.annotation.Keep { *; }
-keep class proguard.annotation.KeepClassMembers { *; }

# Razorpay SDK recommended rules
-keep class com.razorpay.** { *; }
-dontwarn com.razorpay.**

# React Native and Expo recommended rules
-keep class com.facebook.react.** { *; }
-dontwarn com.facebook.react.**
-keep class com.facebook.** { *; }
-dontwarn com.facebook.**
-keep class expo.modules.** { *; }
-dontwarn expo.modules.**

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep annotations
-keep @interface *

# Keep all class members annotated with @Keep
-keepclassmembers class * {
    @proguard.annotation.Keep <fields>;
    @proguard.annotation.Keep <methods>;
}

# Add any project specific keep options here:
