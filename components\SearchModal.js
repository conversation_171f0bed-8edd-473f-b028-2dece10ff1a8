import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './SearchModal.styles';

const SearchModal = ({
  visible,
  onClose,
  searchQuery,
  setSearchQuery,
  categories,
  onCategorySelect,
  onSearchSubmit
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // Sync local search query with global search query when modal opens
  useEffect(() => {
    if (visible) {
      setLocalSearchQuery(searchQuery);
    }
  }, [visible, searchQuery]);

  const handleCategoryPress = (category) => {
    onCategorySelect(category);
    onClose();
  };

  const handleSearchSubmit = () => {
    const trimmedQuery = localSearchQuery.trim();
    if (trimmedQuery) {
      onSearchSubmit(trimmedQuery);
      setSearchQuery(trimmedQuery);
      onClose();
    } else {
      // If empty search, clear search
      handleSearchClear();
      onClose();
    }
  };

  const handleSearchClear = () => {
    setLocalSearchQuery('');
    setSearchQuery('');
  };

  const handleModalClose = () => {
    // Reset local search query to match the current search query when closing
    setLocalSearchQuery(searchQuery);
    onClose();
  };

  const renderCategoryItem = ({ item: category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(category)}
    >
      <Text style={styles.categoryItemText}>{category}</Text>
      <Ionicons name="chevron-forward" size={20} color="#666" />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleModalClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Search & Browse</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleModalClose}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Search Input */}
          <View style={styles.searchInputContainer}>
            <Ionicons
              name="search"
              size={20}
              color="#666"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for clothing items..."
              placeholderTextColor="#000"
              value={localSearchQuery}
              onChangeText={setLocalSearchQuery}
              onSubmitEditing={handleSearchSubmit}
              returnKeyType="search"
              autoFocus={true}
            />
            {localSearchQuery ? (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleSearchClear}
              >
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              style={styles.searchButton}
              onPress={handleSearchSubmit}
            >
              <Ionicons name="arrow-forward" size={20} color="#007AFF" />
            </TouchableOpacity>
          </View>

          {/* Categories List */}
          <Text style={styles.sectionTitle}>Browse Categories</Text>
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item}
            showsVerticalScrollIndicator={false}
            style={styles.categoriesList}
          />
        </View>
      </View>
    </Modal>
  );
};

export default SearchModal;
